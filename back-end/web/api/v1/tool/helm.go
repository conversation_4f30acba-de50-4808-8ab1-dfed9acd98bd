package tool

import (
	"fmt"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/helm"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

func BuildHelmChart(c *gin.Context) {
	user, _ := auth.GetUser(c)
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	apps := make([]string, 0, 0)
	if strings.TrimSpace(app) != "" {
		apps = strings.Split(app, ",")
	}
	overrideNamespace := c.Query("overrideNamespace")

	if cluster == "" || namespace == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "请选择cluster和namespace")
		return
	}
	cacheKey := key.Pre().OPERATION.Key("helmChartBuildJob")

	var err error
	if _, found := cache.GetStr(cacheKey); found {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("已经任务正在进行中, param : %s/%s", cluster, namespace))
		return
	}
	err = cache.SetStr(cacheKey, "running", 2*time.Hour)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	jobId := "helm-chart-build-job-" + time.Now().Format("0102150405")
	pipes, err := pipeline_service.FindByClusterAndNamespace(cluster, namespace)
	pipes2 := make([]models.Pipeline, 0, len(pipes))
	for _, pipe := range pipes {
		if len(apps) != 0 && !strslice.Find(apps, pipe.App) {
			continue
		}
		pipes2 = append(pipes2, pipe)
	}
	go buildHelmChartJob(pipes2, overrideNamespace, user.RealName, jobId, cacheKey)
	web.SuccessJson(c, "job id: "+jobId)
}

func buildHelmChartJob(pipes []models.Pipeline, overrideNamespace, username, jobId, cacheKey string) {
	data := make([]map[string]string, 0, len(pipes))
	releases := make([]helm.Release, 0, len(data))

	for _, pipe := range pipes {
		if overrideNamespace == "" {
			overrideNamespace = pipe.Namespace
		}
		item := make(map[string]string)
		item["app"] = pipe.App
		item["cluster"] = pipe.Cluster
		item["namespace"] = overrideNamespace
		//  build helmfile values
		values, chart, err := helm.BuildHelmfileValuesAndChart(pipe)
		if err != nil {
			item["status"] = "失败，" + err.Error()
			data = append(data, item)
			continue
		}

		// namespace 本身是一个变量，取Helmfile定义的namespace
		namespaceVal := "{{ .Namespace }}"

		for idx, it := range values.ExtraEnvVars {
			if it.Name == "CATALINA_OPTS" {
				values.ExtraEnvVars[idx].Value = fmt.Sprintf("-Dprocess.profile=%s -Dspring.profiles.active=%s -Dprocess.name=%s -Dapp.name=%s",
					namespaceVal, namespaceVal, pipe.App, pipe.App)
			} else if it.Name == "K8S_PROCESS_NAME" {
				values.ExtraEnvVars[idx].Value = pipe.App
			}
		}

		for idx, it := range values.Deployment.InitContainers {
			// 把前面的域名替换为模板变量
			// 所有的的业务镜像（InitContainer) 都存放在 harbor的 artifact 项目里
			parts := strings.SplitN(it.Image, "/artifact/", 2)
			if len(parts) != 2 {
				values.Deployment.InitContainers[idx].Image = "{{ .Values.imageRegistry }}/" + strings.SplitN(it.Image, "/", 2)[1]
				continue
			}
			values.Deployment.InitContainers[idx].Image = "{{ .Values.imageRegistry }}/artifact/" + parts[1]
		}

		releases = append(releases, helm.Release{
			Name:           pipe.App,
			Chart:          chart.Name,
			Version:        chart.Version,
			HelmfileValues: values,
		})

		data = append(data, item)
	}

	log_service.Create(username, jobId, "helm", data)

	helmfile, err := helm.BuildHelmfileValue(releases)
	if err != nil {
		helmfile = "生成helmfile失败，" + err.Error()
	}
	log_service.Create(username, jobId, "helmfile", helmfile)
	cache.Delete(cacheKey)
}
