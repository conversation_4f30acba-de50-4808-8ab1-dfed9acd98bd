package cronjob

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/notify_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"github.com/go-co-op/gocron"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
	"os"
	"strings"
	"time"
)

func client() *resty.Client {
	return resty.New().SetHostURL("http://localhost")
}

func portDump() (err error) {
	var repo *resty.Response
	repo, err = client().R().
		SetHeader("Content-Type", "application/json").
		Post("/api/operation/port/dump")
	if err == nil {
		if repo.StatusCode() != 200 {
			err = errors.New(string(repo.Body()))
		}
	}
	return
}

//func ImageGC() (err error) {
//	var repo *resty.Response
//	repo, err = client().R().
//		SetHeader("Content-Type", "application/json").
//		Post("/api/operation/harbor/gc?dryRun=false")
//	if err == nil {
//		if repo.StatusCode() != 200 {
//			err = errors.New(string(repo.Body()))
//		}
//	}
//	return
//}

func clearLog() (err error) {
	var repo *resty.Response
	repo, err = client().R().
		SetHeader("Content-Type", "application/json").
		Post("/api/operation/log/clear")
	if err == nil {
		if repo.StatusCode() != 200 {
			err = errors.New(string(repo.Body()))
		}
	}
	return
}

func clearMonitorLog() (err error) {
	var repo *resty.Response
	repo, err = client().R().
		SetHeader("Content-Type", "application/json").
		Post("/api/operation/monitor/log/clear")
	if err == nil {
		if repo.StatusCode() != 200 {
			err = errors.New(string(repo.Body()))
		}
	}
	return
}

func appHistoryVersionDump() (err error) {
	var repo *resty.Response
	repo, err = client().R().
		SetHeader("Content-Type", "application/json").
		Post("/api/operation/app-version-history/dump")
	if err == nil {
		if repo.StatusCode() != 200 {
			err = errors.New(string(repo.Body()))
		}
	}
	return
}

func tomcatTrafficCount() (err error) {
	var repo *resty.Response
	repo, err = client().R().
		SetHeader("Content-Type", "application/json").
		Post("/api/operation/tomcat/traffic/count?cluster=k8s1&namespace=foneshare01")
	if err == nil {
		if repo.StatusCode() != 200 {
			err = errors.New(string(repo.Body()))
		}
	}
	return
}

func createTestEventForNoDataMonitor() {
	//为解决部分专属云Event的NoData的告警问题，创建一个测试事件
	eventDTO := dto.Event{
		Namespace:          "default",
		InvolvedObjectType: "Namespace",
		InvolvedObjectName: "default",
		Type:               "Normal",
		Reason:             "NoDataEventTest",
		Message:            "test event for no-data alarm, please ignore it",
		Action:             "Create",
	}
	for _, clu := range config.GetSetting().Clusters {
		if e := k8s_service.CreateEvent(clu.Name, &eventDTO); e != nil {
			log.Warnf("create event for no-data alarm fail, %s", e.Error())
		}
	}
}

func phoneCallTest() error {
	return notify_service.PhoneCall("18600053550", 304, "电话告警测试")
}

// scaleDownOncallScaledApps 缩容哪些由oncall触发了扩容的应用
func scaleDownOncallScaledApps() {
	logs, err := log_service.Search("", "oncall-scale-up", "", 1, 1000)
	if err != nil {
		log.Errorf("search log fail, %s", err.Error())
		return
	}
	startTime := time.Now().Add(-3 * 24 * time.Hour)
	for _, logItem := range logs {
		if logItem.CreatedAt.Before(startTime) {
			continue
		}
		items := strings.Split(logItem.Target, "/")
		if len(items) != 3 {
			log.Warnf("invalid log item target, %s", logItem.Target)
			continue
		}
		cluster := items[0]
		namespace := items[1]
		app := items[2]
		pipe, err := pipeline_service.FirstInEnv(cluster, namespace, app)
		if err != nil {
			log.Warnf("get pipeline fail, %s", err.Error())
			continue
		}
		dep, err := k8s_service.DeploymentDetail(cluster, namespace, app)
		if err != nil {
			log.Warnf("get deployment fail, %s", err.Error())
			continue
		}
		depReplicas := uint(*dep.Spec.Replicas)
		if depReplicas < 5 {
			log.Info("current replicas < 5, skip oncall scale down, target: ", logItem.Target)
			return
		}
		newReplicas := pipe.Replicas + 3
		if depReplicas > newReplicas {
			err := kubectl.Scale(cluster, namespace, app, int32(newReplicas))
			if err == nil {
				log_service.CreateBySys("OnCall-服务缩容", logItem.Target, fmt.Sprintf("replicas: %d -> %d", depReplicas, newReplicas))
				event_service.Create("服务-自动缩容-OnCall", event_service.BuildAppKey(cluster, namespace, app),
					fmt.Sprintf("缩容由Oncall所触发的扩容副本，副本数: %d → %d", depReplicas, newReplicas))
			} else {
				log.Warnf("scale %s deployment fail, %s", logItem.Target, err.Error())
			}
		}
	}
}

func tryLock() bool {
	lockKey := key.Pre().JOB.Key("lockKey")
	//获取锁
	if _, found := cache.GetStr(lockKey); found {
		return false
	}
	val, _ := os.Hostname()
	if val == "" {
		val = "unknown"
	}
	//加锁
	cache.SetStr(lockKey, val, 2*time.Minute)
	return true
}

func Run() {
	if !tryLock() {
		//没有拿到锁，则退出
		return
	}
	s := gocron.NewScheduler(time.Local)
	if _, err := s.Cron("0 1 * * *").Do(clearLog); err != nil {
		log.Warnf("cron job clearLog() fail, %s", err.Error())
	} else {
		log.Info("cron job clearLog() success")
	}

	//if _, err := s.Cron("0 4 * * *").Do(ImageGC); err != nil {
	//	log.Warnf("cron job imageGC() fail, %s", err.Error())
	//} else {
	//	log.Info("cron job imageGC() success")
	//}

	if _, err := s.Cron("10 1 * * *").Do(clearMonitorLog); err != nil {
		log.Warnf("cron job clearMonitorLog() fail, %s", err.Error())
	} else {
		log.Info("cron job clearMonitorLog() success")
	}

	if _, err := s.Cron("0 3 * * *").Do(appHistoryVersionDump); err != nil {
		log.Warnf("cron job appHistoryVersionDump() fail, %s", err.Error())
	} else {
		log.Info("cron job appHistoryVersionDump() success")
	}

	if _, err := s.Cron("0 2 * * *").Do(portDump); err != nil {
		log.Warnf("cron job portDump() fail, %s", err.Error())
	} else {
		log.Info("cron job portDump() success")
	}

	if _, err := s.Cron("0 22 * * *").Do(tomcatTrafficCount); err != nil {
		log.Warnf("cron job tomcatTrafficCount() fail, %s", err.Error())
	} else {
		log.Info("cron job tomcatTrafficCount() success")
	}

	if _, err := s.Every(5).Minute().SingletonMode().Do(cronScale); err != nil {
		log.Warnf("cron job cronScale() fail, %s", err.Error())
	} else {
		log.Info("cron job cronScale() success")
	}

	//发布系统的自动扩缩容在2025年5月被下线
	//if _, err := s.Every(1).Minute().SingletonMode().Do(autoScale); err != nil {
	//	log.Warnf("cron job autoScale() fail, %s", err.Error())
	//} else {
	//	log.Info("cron job autoScale() success")
	//}

	if _, err := s.Cron("0 * * * *").Do(reboot); err != nil {
		log.Warnf("cron job reboot() fail, %v", err)
	} else {
		log.Info("cron job reboot() success")
	}

	//if _, err := s.Cron("0 4 * * *").Do(tool.CheckVersionOfDedicatedCloudApp); err != nil {
	//	log.Warnf("cron job SaveToEs() fail, %s", err.Error())
	//} else {
	//	log.Info("cron job SaveToEs() success")
	//}

	if _, err := s.Cron("0 22 * * *").Do(phoneCallTest); err != nil {
		log.Warnf("cron job phoneCallTest() fail, %s", err.Error())
	} else {
		log.Info("cron job phoneCallTest() success")
	}

	if _, err := s.Cron("0 23 * * *").Do(scaleDownOncallScaledApps); err != nil {
		log.Warnf("cron job scaleDownOncallScaledApps() fail, %s", err.Error())
	} else {
		log.Info("cron job scaleDownOncallScaledApps() success")
	}

	if _, err := s.Cron("0 22 * * *").Do(cmdb_service.SyncCmdbFromCRM); err != nil {
		log.Warnf("cron job syncCmdbFromCRM() fail, %s", err.Error())
	} else {
		log.Info("cron job syncCmdbFromCRM() success")
	}

	if _, err := s.Cron("30 22 * * *").Do(app_service.SyncFromCMDB); err != nil {
		log.Warnf("cron job syncCmdbFromCRM() fail, %s", err.Error())
	} else {
		log.Info("cron job syncCmdbFromCRM() success")
	}

	if _, err := s.Cron("0 * * * *").Do(createTestEventForNoDataMonitor); err != nil {
		log.Warnf("cron job createTestEventForNoDataMonitor() fail, %s", err.Error())
	} else {
		log.Info("cron job createTestEventForNoDataMonitor() success")
	}

	s.StartAsync()
}
