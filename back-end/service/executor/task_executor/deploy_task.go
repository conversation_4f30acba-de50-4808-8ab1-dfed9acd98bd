package task_executor

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	k8s_templates "fs-k8s-app-manager/k8s-templates"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/client/kafka"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/event_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"github.com/mitchellh/mapstructure"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/intstr"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type DeployParam struct {
	PipelineId    uint                   `json:"pipelineId"`
	DeployModules datatype.DeployModules `json:"deployModules"`
	MaxSurge      string                 `json:"maxSurge"`
	Remark        string                 `json:"remark" `
	FailRollout   bool                   `json:"failRollout"`
}

type DeployTaskExecutor struct {
	TaskAbstractExecutor
	Params DeployParam
}

func (s DeployTaskExecutor) Build(t *models.Task) (TaskExecutor, error) {
	if err := mapstructure.Decode(t.Params, &s.Params); err != nil {
		return s, err
	}
	s.Task = t
	return s, nil
}

func (s DeployTaskExecutor) Run() {
	defer s.catchPanic()
	if s.Task.IsSkip() {
		panic("task is skip")
	}
	if !s.Task.IsWait() {
		panic("task is not wait")
	}
	if err := s.Start(); err != nil {
		_ = s.Fail()
		panic(err.Error())
	}
	if err := s.process(); err != nil {
		panic(err.Error())
	}
	if !s.Task.IsCancel() {
		if err := s.Success(); err != nil {
			panic(err.Error())
		}
	}
}

func (s DeployTaskExecutor) process() error {
	start := time.Now()

	pipe, err := pipeline_service.FindById(s.Params.PipelineId)
	if err != nil {
		return err
	}
	clu := config.GetSetting().GetCluster(pipe.Cluster)
	if clu == nil {
		return errors.New("cluster not found")
	}

	p, err := s.buildTemplateParam(pipe, *clu)
	if err != nil {
		return err
	}
	//从制品镜像仓库下载制品
	deploymentFileName := "deployment.yaml.tmpl"

	s.appendMetricsPort(&p)

	dYaml, err := k8s_templates.BuildDeploymentWithName(p, deploymentFileName)
	if err != nil {
		return err
	}

	if strings.Contains(dYaml, "[系统赋值]") {
		return errors.New("yaml文件里存在没有赋值的内容 [系统赋值]")
	}

	output := "--- Deployment Update ---\n" + dYaml
	_ = s.AppendOutput(output)
	//集中发版时， 有时会出现timeout. 针对这种情况做一定的尝试
	for retry := 0; retry < 5; retry++ {
		//k8s的merge可能会导致部分数组元素无法按预期删除， 因此更新操作需要使用replace
		op := "replace"
		if _, er := k8s_service.DeploymentDetail(p.Cluster, p.Namespace, p.App); k8s_errors.IsNotFound(er) {
			op = "apply"
		}
		if err = kubectl.ApplyOrReplace(p.Cluster, p.Namespace, dYaml, op); err != nil {
			_ = s.AppendOutput(err.Error())
			time.Sleep(5 * time.Second)
			continue
		}
		break
	}
	event_service.Create(s.Task.Author, event_service.BuildAppKey(p.Cluster, p.Namespace, p.App),
		fmt.Sprintf("服务发布，副本数：%d, 版本号：%s,", p.Replicas, s.Params.DeployModules.Version()))
	if err != nil {
		return err
	}

	_ = s.AppendOutput("--- waiting for deploy to complete ---")
	var loopErr error = nil
	for {
		_ = s.ReloadTask()
		if s.Task.IsCancel() {
			_ = s.AppendOutput("task canceled, rolling back app version")
			if _, err := kubectl.RolloutUndo(p.Cluster, p.Namespace, p.App); err != nil {
				_ = s.AppendOutput("rollback failed, " + err.Error())
			}
			break
		}
		output, err := kubectl.WaitDeployRollout(p.Cluster, p.Namespace, p.App, 20)
		_ = s.AppendOutput(output)
		//成功状态
		if err == nil {
			break
		}

		if time.Now().Unix()-start.Unix() > int64(s.Task.TimeoutSeconds) {
			loopErr = errors.New(fmt.Sprintf("<add-to-title>ERROR: 发布超时，超时时间: %d秒。请通过容器启动日志查看启动为什么很慢？</add-to-title>", s.Task.TimeoutSeconds))
			break
		}

	}
	if loopErr != nil && p.FailRollout {
		_ = s.AppendOutput(loopErr.Error())
		_ = s.AppendOutput("task failed, rolling back app version")
		_, err := kubectl.RolloutUndo(p.Cluster, p.Namespace, p.App)
		if err != nil {
			_ = s.AppendOutput("rollback failed," + err.Error())
		}
	}
	if loopErr == nil {
		//update service
		if err = s.updateService(pipe); err != nil {
			return fmt.Errorf("k8s service update fail, %s", err.Error())
		}

		//update ingress
		if clu.IngressParentHost != "" {
			ingressTmpl := "ingress.yaml.tmpl"
			if clu.IngressTmpl != "" {
				ingressTmpl = clu.IngressTmpl
			}
			if err = s.updateIngress(pipe, clu.IngressParentHost, ingressTmpl); err != nil {
				return fmt.Errorf(" update k8s ingress fail, %s", err.Error())
			}
		}

		//update prometheus monitor
		if sm := clu.PrometheusMonitor; sm.Enable && len(sm.Prometheus) > 0 && hasTrueValue(p.Envs, constant.ENV_KEY_SPRING_BOOT_METRICS_ENABLE) {
			//只有主动启用的才部署ServiceMonitor，否则会有大量的404请求
			if err = s.updateServiceMonitor(pipe, clu.PrometheusMonitor); err != nil {
				return fmt.Errorf("update k8s ServiceMonitor fail, %s", err.Error())
			}
		}
	}
	_ = kafka.SendDevopsEvent(kafka.DevopsEvent{
		Cluster:      p.Cluster,
		Profile:      p.Namespace,
		Creator:      s.Task.Author,
		ResourceType: "app",
		ResourceId:   p.App,
		Title:        "应用发版",
		StartTime:    s.Task.StartTime.Time().UnixMilli(),
		EndTime:      time.Now().UnixMilli(),
		Message:      fmt.Sprintf("版本: %s | 描述：%s", s.Params.DeployModules.Version(), s.Params.Remark),
		Level:        "info",
		Extra:        "",
	})
	return loopErr
}

func (s DeployTaskExecutor) buildTemplateParam(pipe models.Pipeline, clu config.Cluster) (ret k8s_templates.DeploymentParam, err error) {
	//阿里jdk的多租户功能需要使用特权， ref: https://github.com/alibaba/dragonwell8/wiki/使用Alibaba-Draognwell多租户特性管控运行时资源
	//todo: 考虑改用发布流程的配置来管理
	privileged := strings.Contains(pipe.EnvValue("JAVA_OPTS"), "-XX:+MultiTenant")

	baseImage := pipe.BaseImage
	if config.GetSetting().BaseImageGray.ImageTagSuffix != "" && config.GetSetting().BaseImageGray.AppContains(pipe.Cluster, pipe.Namespace, pipe.App) {
		baseImage = pipe.BaseImage + config.GetSetting().BaseImageGray.ImageTagSuffix
	}
	envs, err := BuildEnvs(pipe)
	if err != nil {
		return ret, err
	}
	p := k8s_templates.DeploymentParam{
		Cluster:              pipe.Cluster,
		Namespace:            pipe.Namespace,
		App:                  pipe.App,
		AppImage:             baseImage,
		ImagePullPolicy:      "Always",
		Replicas:             pipe.Replicas,
		MaxSurge:             s.Params.MaxSurge,
		DeployId:             fmt.Sprintf("%d", s.Task.ID),
		Annotations:          make(map[string]string),
		Labels:               make(map[string]string),
		PodAnnotations:       make(map[string]string),
		Resources:            pipe.Resources,
		DeployStrategy:       pipe.DeployStrategy,
		LivenessProbe:        pipe.LivenessProbe,
		ReadinessProbe:       pipe.ReadinessProbe,
		StartupProbe:         pipe.StartupProbe,
		Schedule:             pipe.Schedule,
		SchedulerName:        clu.SchedulerName,
		PVC:                  pipe.PVC,
		Envs:                 envs,
		Ports:                pipe.Ports,
		PartnerApps:          pipe.PartnerApps,
		ExclusiveApps:        pipe.ExclusiveApps,
		PreStopWebhook:       pipe.PreStopWebhook,
		PreStopRetainSeconds: pipe.PreStopRetainSeconds,
		AddSysctlKeepalive:   pipe.Options.AddSysctlKeepalive,
		Privileged:           privileged,
		TomcatLogEmptyDir:    clu.EmptyDir.TomcatLog,
		AppLogsEmptyDir:      clu.EmptyDir.AppLogs,
		FailRollout:          s.Params.FailRollout,
		RevisionHistoryLimit: 8,
		DNSConfigSearches:    clu.DnsConfigSearches,
		EnableServiceLinks:   true, // 默认为true
	}

	// 设置 EnableServiceLinks
	// 如果应用在禁用列表中，则设置为 false，否则使用默认值 true
	p.EnableServiceLinks = !config.GetSetting().IsServiceLinksDisabled(pipe.Cluster, pipe.Namespace, pipe.App)
	if !strings.Contains(p.Namespace, "foneshare") {
		p.RevisionHistoryLimit = 5
	}
	//复制云是私有云（比如招商局）的模版云，尽量保留多的历史版本，避免容器镜像GC时把招商局环境在用的镜像清理掉了
	if p.Cluster == "forceecrm-k8s1" {
		p.RevisionHistoryLimit = 30
	}

	if clu.ImagePullPolicy != "" {
		p.ImagePullPolicy = clu.ImagePullPolicy
	}

	p.Port80To8080 = strings.HasSuffix(p.AppImage, "-port8080")
	p.Annotations["kubernetes.io/change-cause"] = s.Params.DeployModules.Version()
	p.Annotations["fxiaoke.com/language"] = "Java"
	p.Annotations["fxiaoke.com/deploy-user"] = s.Task.Author
	p.Annotations["fxiaoke.com/deploy-time"] = time.Now().Format("2006-01-02 15:04")
	p.Annotations["fxiaoke.com/deploy-remark"] = strings.NewReplacer("\n", "", "\"", "").Replace(s.Params.Remark)
	p.Annotations["fxiaoke.com/last-modify-user"] = s.Task.Author
	p.Annotations["fxiaoke.com/node-dedicated"] = p.Schedule.Node

	if _a, _ := app_service.FindByName(p.App); _a.Level != "" {
		p.PodAnnotations["fxiaoke.com/server-level"] = _a.Level
	}

	artifacts := make([]k8s_templates.Artifact, 0, len(s.Params.DeployModules))
	for _, it := range s.Params.DeployModules {
		artifacts = append(artifacts, k8s_templates.Artifact{
			GitUrl:        it.GitUrl,
			Module:        it.Module,
			ContextPath:   it.ContextPath,
			Tag:           it.Tag,
			CommitID:      it.CommitID,
			ArtifactImage: it.ArtifactImage,
			ArtifactSrc:   it.ArtifactPathSrc,
			ArtifactDst:   it.ArtifactPathDst,
		})
	}
	p.Artifacts = artifacts

	// init containers for shared path
	if len(pipe.ExtInitContainer) > 0 {
		split := strings.Split(pipe.ExtInitContainer, ",")
		// 临时借用Artifact这个对象后续考虑合并一起
		containers := make([]k8s_templates.Artifact, 0, len(split))
		for _, it := range split {
			containers = append(containers, k8s_templates.Artifact{
				ArtifactImage: strings.TrimSpace(it),
			})
		}
		p.ExtInitContainers = containers
	}

	p.AppImage = clu.NormalImage2ProxyImage(p.AppImage)
	for idx, it := range p.Artifacts {
		p.Artifacts[idx].ArtifactImage = clu.NormalImage2ProxyImage(it.ArtifactImage)
	}

	//todo: 需要确认podAntiAffinity对专用资源池的影响，然后放开ExclusiveApps配置
	//由于podAntiAffinity会影响到应用对专用资源池的使用（即使nodeAffinity的weight 比 podAntiAffinity weight高很多），这里暂且关闭掉ExclusiveApps配置
	p.ExclusiveApps = nil
	p.PartnerApps = nil
	return p, nil
}
func (s DeployTaskExecutor) appendMetricsPort(param *k8s_templates.DeploymentParam) {
	//修正 deploy metrics por(service port 都是8090)，但是deploy里的port，Spring boot jar应用是8081，Spring boot war应用如果启用Actuator是等同于 http port (80或8080)，其他的使用 jmx agent 端口8090
	metricsPort := constant.JMX_AGENT_METRICS_PORT
	isJar := hasTrueValue(param.Envs, constant.ENV_KEY_SPRING_BOOT_JAR_APP)
	enableMetrics := hasTrueValue(param.Envs, constant.ENV_KEY_SPRING_BOOT_METRICS_ENABLE)
	// 是spring boot部署成war的模式，且启动了Actuator Prometheus，与业务端口共用
	if enableMetrics {
		//是spring boot以jar形式启动，且启动了Actuator Prometheus
		if isJar {
			metricsPort = constant.SPRING_BOOT_ACTUATOR_PORT
		} else {
			for _, pt := range param.Ports {
				if pt.Name == "http" {
					metricsPort = pt.Value
				}
			}
		}
	}
	param.Ports = append(param.Ports, datatype.Port{
		Name:  constant.METRICS_PORT_NAME,
		Value: metricsPort,
		Type:  constant.PORT_TYPE_SYSTEM,
	})
}

func hasTrueValue(ev datatype.Envs, key string) bool {
	v := ev.FindByName(key)
	return v != nil && v.Value == "true"
}

func (s DeployTaskExecutor) updateServiceMonitor(pipe models.Pipeline, p config.PrometheusMonitor) error {
	endpoints := make([]k8s_templates.Endpoint, 0, len(s.Params.DeployModules))
	for _, it := range s.Params.DeployModules {
		endpoints = append(endpoints, k8s_templates.Endpoint{
			Path:          metricsPath(p, it, pipe),
			Port:          constant.METRICS_PORT_NAME,
			Interval:      p.MetricsInterval,
			ScrapeTimeout: p.ScrapeTimeout,
		})
	}
	ps := k8s_templates.PrometheusMonitorParam{
		App:        pipe.App,
		Namespace:  pipe.Namespace,
		Prometheus: p.Prometheus,
		Endpoints:  endpoints,
	}
	sYaml, err := k8s_templates.BuildPodMonitor(ps)
	if err != nil {
		return err
	}
	output := "--- Prometheus PodMonitor Update ---\n" + sYaml
	_ = s.AppendOutput(output)
	return kubectl.Apply(pipe.Cluster, pipe.Namespace, sYaml)
}

func metricsPath(sm config.PrometheusMonitor, it datatype.DeployModule, pipe models.Pipeline) string {
	//spring boot jar 形式走独立的端口，path也是固定的
	if it.ContextPath == "" || it.ContextPath == "/" || hasTrueValue(pipe.Envs, constant.ENV_KEY_SPRING_BOOT_JAR_APP) {
		return sm.MetricsPath
	}
	return it.ContextPath + sm.MetricsPath
}

func (s DeployTaskExecutor) updateService(p models.Pipeline) error {
	k8sSer, err := k8s_service.ServiceDTO(p.Cluster, p.Namespace, p.App)
	if err != nil {
		return err
	}

	//如果targetPort不是数字类型，则更新service, 使其转换为字符串类型
	if k8sSer != nil &&
		k8sSer.Ports[0].TargetPort.Type == intstr.String &&
		!portsChanged(p.Ports, k8sSer.Ports) {
		return nil
	}

	nodeportPool := make(map[int32]int32)
	if k8sSer != nil {
		for _, port := range k8sSer.Ports {
			nodeportPool[port.Port] = port.NodePort
		}
	} else {
		// 专属云环境: 让专属云服务的NodePort尽量与模版云（cloudmodel-k8s1）保持一致
		// 专属云可能包含多个环境，当前只保持主环境（后缀不包含数字）的NodePort一致性
		if !config.GetSetting().GetCluster(p.Cluster).IsFxiaokeCloud() &&
			p.Cluster != "cloudmodel-k8s1" &&
			!regexp.MustCompile(`\d$`).MatchString(p.Namespace) {
			pipelines, err := pipeline_service.FindByApp(p.App)
			if err != nil {
				return err
			}
			//参考发布流程
			var refPipe *models.Pipeline
			for _, pipe := range pipelines {
				if pipe.Cluster == "cloudmodel-k8s1" {
					refPipe = &pipe
					break
				}
			}

			if refPipe != nil {
				if v, _ := k8s_service.ServiceDTO(refPipe.Cluster, refPipe.Namespace, refPipe.App); v != nil {
					for _, port := range v.Ports {
						nodeportPool[port.Port] = port.NodePort
					}
				}
			}
		}
	}

	// apply k8s service
	// 如果端口已经分配过NodePort，这里一定要带上。否则会导致端口NodePort值变动的问题，这会是一个很严重的问题
	//关于kubectl apply merge机制及相关问题讨论可参考官方文档或者 https://github.com/kubernetes/kubernetes/issues/23551
	ports := make([]k8s_templates.Port, 0, len(p.Ports))
	for _, it := range p.Ports {
		port := int32(it.Value)
		nodePort := nodeportPool[port]
		ports = append(ports, k8s_templates.Port{
			Name:          it.Name,
			Port:          port,
			TargetPortStr: it.Name,
			NodePort:      nodePort,
		})
	}

	ps := k8s_templates.ServiceParam{
		App:       p.App,
		Namespace: p.Namespace,
		Ports:     ports,
	}
	sYaml, err := k8s_templates.BuildService(ps)
	if err != nil {
		return err
	}
	log_service.Create(s.Task.Author, "K8S-Service-Apply", p.App, sYaml)
	output := "--- Service Update ---\n" + sYaml
	_ = s.AppendOutput(output)
	//todo: apply无法删除端口，可以考虑使用replace方式处理，但需要考虑风险
	return kubectl.Apply(p.Cluster, p.Namespace, sYaml)
}

func (s DeployTaskExecutor) updateIngress(p models.Pipeline, ingressParentHost string, ingressTmpl string) error {
	found, err := k8s_service.IngressExist(p.Cluster, p.Namespace, p.App)
	if err != nil {
		return err
	}
	//todo: 最好是判断ingress内容是否有修改来决定是否继续
	if found {
		return nil
	}

	//nginx ingress reload: https://kubernetes.github.io/ingress-nginx/how-it-works/#when-a-reload-is-required
	if !config.GetSetting().IngressReloadAllowInDay {
		currHour := time.Now().Hour()
		if 6 < currHour && currHour < 22 {
			return errors.New("平台限制：Ingress只能在22:00-07:00之间执行Reload操作")
		}
	}

	pi := k8s_templates.IngressParam{
		App:         p.App,
		Namespace:   p.Namespace,
		IngressHost: fmt.Sprintf("%s.%s", p.Namespace, ingressParentHost),
	}
	iYaml, err := k8s_templates.BuildIngressWithName(pi, ingressTmpl)
	if err != nil {
		return err
	}
	output := "--- Ingress Update ---\n" + iYaml
	_ = s.AppendOutput(output)
	return kubectl.Apply(p.Cluster, p.Namespace, iYaml)
}

// 服务端口是否有变更？
func portsChanged(pipePorts datatype.Ports, servicePorts []dto.ServicePort) bool {
	if len(pipePorts) != len(servicePorts) {
		return true
	}
	for _, src := range pipePorts {
		exist := false
		for _, dst := range servicePorts {
			if int32(src.Value) == dst.Port {
				exist = true
				break
			}
		}
		if !exist {
			return true
		}
	}
	return false
}

// 构建环境变量
func BuildEnvs(pipe models.Pipeline) (datatype.Envs, error) {
	envs := pipe.Envs
	//SkyWalking agent
	envs = append(envs, datatype.Env{
		Name:  constant.ENV_KEY_SKY_WALKING_ENABLE,
		Value: strconv.FormatBool(pipe.Options.SkyWalkingAgent),
		Type:  constant.ENV_TYPE_SYSTEM,
	})

	// app-log
	envs = append(envs, datatype.Env{
		Name:  constant.ENV_KEY_APP_LOG_TO_KAFKA,
		Value: strconv.FormatBool(pipe.Options.AppLogToKafka),
		Type:  constant.ENV_TYPE_SYSTEM,
	})

	// jvm gc log
	envs = append(envs, datatype.Env{
		Name:  constant.ENV_KEY_JVM_GC_LOG_ENABLE,
		Value: strconv.FormatBool(pipe.Options.JvmGcLog),
		Type:  constant.ENV_TYPE_SYSTEM,
	})

	//jacoco agent
	envs = append(envs, datatype.Env{
		Name:  constant.ENV_KEY_JACOCO_AGENT_ENABLE,
		Value: strconv.FormatBool(pipe.Namespace == "jacoco"),
		Type:  constant.ENV_TYPE_SYSTEM,
	})

	//java_opts内容处理
	//阿里jdk(dragonwell8)镜像里开启了G1ElasticHeap功能，该功能必须要配置-Xmx参数，并且要求Xms等于Xmx，否则会启动报错。
	//参考：https://github.com/dragonwell-project/dragonwell8/issues/441
	if strings.Contains(pipe.BaseImage, "ali-dragonwell") {
		for idx, it := range envs {
			switch it.Name {
			case "JAVA_OPTS":
				if !strings.Contains(it.Value, "-Xmx") {
					//G1ElasticHeap会根据MaxNewSize来决定可回收的提交内存，降低该值可以让JVM尽量多回收一些提交内存
					//经测试，MaxNewSize默认为最大堆的2/3
					xmx := int32(pipe.Resources.LimitMemory / 2)
					xms := xmx
					maxNewSize := xmx / 4
					if maxNewSize < 100 && xmx > 100 {
						maxNewSize = 100
					}
					envs[idx].Value = it.Value + fmt.Sprintf(" -Xms%dm -Xmx%dm -XX:MaxNewSize=%dm ", xms, xmx, maxNewSize)
				} else {
					//如果已经自定义了-Xmx，为了确保-Xms等于-Xmx，这里追加-Xms参数（可覆盖自定义的-Xms参数）
					re := regexp.MustCompile(`-Xmx(\d+[mMgG])`)
					matches := re.FindStringSubmatch(it.Value)
					if len(matches) != 2 {
						return nil, errors.New("无法解析Xmx参数值，请联系管理员处理")
					}
					envs[idx].Value = it.Value + fmt.Sprintf(" -Xms"+matches[1]+" ")
				}
			}
		}
	}

	cluster := config.GetSetting().GetCluster(pipe.Cluster)
	if cluster != nil {
		//dubbo helper
		envs = append(envs, datatype.Env{
			Name:  "DUBBO_HELPER_URL",
			Value: cluster.ThirdServices.DubboHelperUrl,
			Type:  constant.ENV_TYPE_SYSTEM,
		})

		//add nacos helper env
		envs = append(envs, datatype.Env{
			Name:  "NACOS_HELPER_URL",
			Value: cluster.ThirdServices.NacosHelperUrl,
			Type:  constant.ENV_TYPE_SYSTEM,
		})
		//add nacos server url, for nacos service register
		envs = append(envs, datatype.Env{
			Name:  "NACOS_SERVER_URL",
			Value: cluster.ThirdServices.NacosServerUrl,
			Type:  constant.ENV_TYPE_SYSTEM,
		})

		if cluster.MallocArena.Enable && cluster.MallocArena.Max > 0 {
			//如果已经自定义了环境变量值，则优先使用
			if v := pipe.Envs.FindByName(constant.ENV_KEY_MALLOC_ARENA_MAX); v == nil {
				envs = append(envs, datatype.Env{
					Name:  constant.ENV_KEY_MALLOC_ARENA_MAX,
					Value: strconv.Itoa(int(cluster.MallocArena.Max)),
					Type:  constant.ENV_TYPE_SYSTEM,
				})
			}
		}
	}

	// CONFIG_NAMESPACE 环境变量
	if cluster != nil {
		configNamespace := cluster.ConfigServiceNamespace
		if configNamespace == "" && len(cluster.Namespaces) > 0 {
			// 如果未配置或为空字符串，使用 namespaces 中的第一个
			configNamespace = cluster.Namespaces[0]
		}
		if configNamespace != "" {
			envs = append(envs, datatype.Env{
				Name:  "CONFIG_NAMESPACE",
				Value: configNamespace,
				Type:  constant.ENV_TYPE_SYSTEM,
			})
		}
	}

	return envs, nil
}
