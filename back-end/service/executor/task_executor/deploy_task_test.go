package task_executor

import (
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/constant"
	"testing"
)

func TestBuildEnvs_ConfigNamespace(t *testing.T) {
	// 创建测试用的 pipeline
	pipe := models.Pipeline{
		Cluster:   "test-cluster",
		Namespace: "test-namespace",
		App:       "test-app",
		Envs:      datatype.Envs{},
	}

	// 测试用例1: configNamespace 在 settings 中配置了值
	t.Run("ConfigNamespace configured in settings", func(t *testing.T) {
		// 模拟 settings 配置
		originalSetting := config.GetSetting()
		
		// 创建测试配置
		testSetting := originalSetting
		testSetting.ConfigNamespace = "custom-namespace"
		testSetting.Clusters = []config.Cluster{
			{
				Name:       "test-cluster",
				Namespaces: []string{"default", "test"},
			},
		}
		
		// 这里我们无法直接修改全局设置，所以我们需要测试逻辑
		// 在实际代码中，我们会从 config.GetSetting() 获取配置
		
		envs, err := BuildEnvs(pipe)
		if err != nil {
			t.Fatalf("BuildEnvs failed: %v", err)
		}
		
		// 查找 CONFIG_NAMESPACE 环境变量
		var configNamespaceEnv *datatype.Env
		for _, env := range envs {
			if env.Name == "CONFIG_NAMESPACE" {
				configNamespaceEnv = &env
				break
			}
		}
		
		// 由于我们无法在测试中修改全局配置，这个测试主要验证代码结构
		// 在实际运行时，如果 settings.json 中配置了 configNamespace，应该会使用该值
		t.Logf("Found %d environment variables", len(envs))
		if configNamespaceEnv != nil {
			t.Logf("CONFIG_NAMESPACE found with value: %s", configNamespaceEnv.Value)
		} else {
			t.Log("CONFIG_NAMESPACE not found (expected if settings.configNamespace is empty)")
		}
	})

	// 测试用例2: configNamespace 未配置，应该使用 namespaces 的第一个
	t.Run("ConfigNamespace not configured, use first namespace", func(t *testing.T) {
		envs, err := BuildEnvs(pipe)
		if err != nil {
			t.Fatalf("BuildEnvs failed: %v", err)
		}
		
		// 验证环境变量数量大于0
		if len(envs) == 0 {
			t.Fatal("Expected at least some environment variables")
		}
		
		// 验证系统环境变量存在
		foundSystemEnvs := 0
		for _, env := range envs {
			if env.Type == constant.ENV_TYPE_SYSTEM {
				foundSystemEnvs++
			}
		}
		
		if foundSystemEnvs == 0 {
			t.Fatal("Expected at least some system environment variables")
		}
		
		t.Logf("Found %d system environment variables", foundSystemEnvs)
	})
}

func TestConfigNamespaceLogic(t *testing.T) {
	// 测试 CONFIG_NAMESPACE 的逻辑
	testCases := []struct {
		name                string
		configNamespace     string
		clusterNamespaces   []string
		expectedNamespace   string
		shouldAddEnv        bool
	}{
		{
			name:              "ConfigNamespace configured",
			configNamespace:   "custom-ns",
			clusterNamespaces: []string{"default", "test"},
			expectedNamespace: "custom-ns",
			shouldAddEnv:      true,
		},
		{
			name:              "ConfigNamespace empty, use first namespace",
			configNamespace:   "",
			clusterNamespaces: []string{"default", "test"},
			expectedNamespace: "default",
			shouldAddEnv:      true,
		},
		{
			name:              "ConfigNamespace empty, no namespaces",
			configNamespace:   "",
			clusterNamespaces: []string{},
			expectedNamespace: "",
			shouldAddEnv:      false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 模拟逻辑
			configNamespace := tc.configNamespace
			if configNamespace == "" && len(tc.clusterNamespaces) > 0 {
				configNamespace = tc.clusterNamespaces[0]
			}
			
			shouldAdd := configNamespace != ""
			
			if shouldAdd != tc.shouldAddEnv {
				t.Errorf("Expected shouldAddEnv=%v, got %v", tc.shouldAddEnv, shouldAdd)
			}
			
			if configNamespace != tc.expectedNamespace {
				t.Errorf("Expected namespace=%s, got %s", tc.expectedNamespace, configNamespace)
			}
		})
	}
}
