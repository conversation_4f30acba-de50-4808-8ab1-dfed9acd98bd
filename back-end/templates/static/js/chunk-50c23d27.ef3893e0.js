(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-50c23d27"],{"11d1":function(e,t,a){},1316:function(e,t,a){},"1e42":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:e.exportExcel}},[e._v("导出")])],1)},n=[],o=(a("a481"),a("25ca")),l=a("21a6"),r=a.n(l),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var e=this.tableRef.$el,t=o["a"].table_to_book(e,{raw:!0}),a=o["b"](t,{bookType:"xlsx",bookSST:!0,type:"array"});try{var i=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";r.a.saveAs(new Blob([a],{type:"application/octet-stream"}),i)}catch(n){this.$message.error("导出失败, err: "+n.message),console.error(n)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,p=a("2877"),u=Object(p["a"])(c,i,n,!1,null,null,null);t["a"]=u.exports},"28a5":function(e,t,a){"use strict";var i=a("aae3"),n=a("cb7c"),o=a("ebd6"),l=a("0390"),r=a("9def"),s=a("5f1b"),c=a("520a"),p=a("79e5"),u=Math.min,d=[].push,m="split",f="length",v="lastIndex",b=4294967295,g=!p((function(){RegExp(b,"y")}));a("214f")("split",2,(function(e,t,a,p){var h;return h="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[f]||2!="ab"[m](/(?:ab)*/)[f]||4!="."[m](/(.?)(.?)/)[f]||"."[m](/()()/)[f]>1||""[m](/.?/)[f]?function(e,t){var n=String(this);if(void 0===e&&0===t)return[];if(!i(e))return a.call(n,e,t);var o,l,r,s=[],p=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),u=0,m=void 0===t?b:t>>>0,g=new RegExp(e.source,p+"g");while(o=c.call(g,n)){if(l=g[v],l>u&&(s.push(n.slice(u,o.index)),o[f]>1&&o.index<n[f]&&d.apply(s,o.slice(1)),r=o[0][f],u=l,s[f]>=m))break;g[v]===o.index&&g[v]++}return u===n[f]?!r&&g.test("")||s.push(""):s.push(n.slice(u)),s[f]>m?s.slice(0,m):s}:"0"[m](void 0,0)[f]?function(e,t){return void 0===e&&0===t?[]:a.call(this,e,t)}:a,[function(a,i){var n=e(this),o=void 0==a?void 0:a[t];return void 0!==o?o.call(a,n,i):h.call(String(n),a,i)},function(e,t){var i=p(h,e,this,t,h!==a);if(i.done)return i.value;var c=n(e),d=String(this),m=o(c,RegExp),f=c.unicode,v=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(g?"y":"g"),_=new m(g?c:"^(?:"+c.source+")",v),y=void 0===t?b:t>>>0;if(0===y)return[];if(0===d.length)return null===s(_,d)?[d]:[];var x=0,w=0,S=[];while(w<d.length){_.lastIndex=g?w:0;var k,C=s(_,g?d:d.slice(w));if(null===C||(k=u(r(_.lastIndex+(g?0:w)),d.length))===x)w=l(d,w,f);else{if(S.push(d.slice(x,w)),S.length===y)return S;for(var D=1;D<=C.length-1;D++)if(S.push(C[D]),S.length===y)return S;w=x=k}}return S.push(d.slice(x)),S}]}))},"2fdb":function(e,t,a){"use strict";var i=a("5ca1"),n=a("d2c8"),o="includes";i(i.P+i.F*a("5147")(o),"String",{includes:function(e){return!!~n(this,e,o).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},"343d":function(e,t,a){"use strict";a("1316")},"4ad4":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-address-wrapper"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("访问地址")]),e._v(" "),a("span",{staticStyle:{display:"inline-block","margin-left":"20px","font-size":"13px"}},[e._v("\n        ( 应用："+e._s(this.app)+" | 环境："+e._s(this.namespace)+" | 集群："+e._s(this.cluster)+" )\n      ")])]),e._v(" "),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.address}},[a("el-table-column",{attrs:{prop:"name",width:"180",label:"端口名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"port",width:"100",label:"端口号"}}),e._v(" "),a("el-table-column",{attrs:{prop:"protocol",width:"100",label:"协议"}}),e._v(" "),a("el-table-column",{attrs:{label:"访问地址"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.addresses,(function(t,i){return a("p",{staticStyle:{margin:"8px"}},[a("b",{staticStyle:{"padding-right":"10px"}},[e._v("地址"+e._s(i+1)+":")]),e._v(e._s(t)+"\n              "),a("clipboard-icon",{staticStyle:{"margin-left":"10px"},attrs:{text:t,"button-text":""}})],1)}))}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"备注"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t.row.remark)}})]}}])})],1)],1)])],1)},n=[],o=a("b562"),l=a("da37"),r={name:"AppAddress",components:{ClipboardIcon:l["a"]},props:{cluster:{type:String,require:!0},namespace:{type:String,require:!0},app:{type:String,require:!0}},data:function(){return{loading:!1,address:[]}},watch:{cluster:function(e){this.showAddress()},namespace:function(e){this.showAddress()},app:function(e){this.showAddress()}},computed:{},mounted:function(){this.showAddress()},methods:{showAddress:function(){var e=this;this.cluster?this.namespace?this.app?(this.loading=!0,Object(o["m"])(this.cluster,this.namespace,this.app).then((function(t){e.address=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))):this.$message.warning("缺少参数 app"):this.$message.warning("缺少参数 namespace"):this.$message.warning("缺少参数 cluster")}}},s=r,c=(a("de9b"),a("2877")),p=Object(c["a"])(s,i,n,!1,null,null,null);t["a"]=p.exports},"504c":function(e,t,a){var i=a("9e1e"),n=a("0d58"),o=a("6821"),l=a("52a7").f;e.exports=function(e){return function(t){var a,r=o(t),s=n(r),c=s.length,p=0,u=[];while(c>p)a=s[p++],i&&!l.call(r,a)||u.push(e?[a,r[a]]:r[a]);return u}}},5147:function(e,t,a){var i=a("2b4c")("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[i]=!1,!"/./"[e](t)}catch(n){}}return!0}},"51a9":function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"e",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"l",(function(){return r})),a.d(t,"m",(function(){return s})),a.d(t,"a",(function(){return c})),a.d(t,"f",(function(){return p})),a.d(t,"i",(function(){return u})),a.d(t,"j",(function(){return d})),a.d(t,"k",(function(){return m})),a.d(t,"n",(function(){return f})),a.d(t,"g",(function(){return v})),a.d(t,"b",(function(){return b})),a.d(t,"h",(function(){return g})),a.d(t,"o",(function(){return h}));var i=a("b775");function n(e){return Object(i["a"])({url:"/v1/pipeline/app/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/v1/pipeline",method:"get",params:{id:e}})}function l(e,t,a){return Object(i["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:e,namespace:t,app:a}})}function r(e){return Object(i["a"])({url:"/v1/pipeline/search",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:e})}function c(){return Object(i["a"])({url:"/v1/pipeline/all",method:"get"})}function p(e){return Object(i["a"])({url:"/v1/pipeline/status",method:"get",params:{status:e}})}function u(e){return Object(i["a"])({url:"/v1/pipeline/init",method:"post",params:{app:e}})}function d(e){return Object(i["a"])({url:"/v1/pipeline",method:"post",data:e})}function m(e){return Object(i["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:e}})}function f(e){return Object(i["a"])({url:"/v1/pipeline/sync",method:"post",data:e})}function v(e,t,a,n){return Object(i["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:n}})}function b(e){return Object(i["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:e})}function g(e,t,a,n){return Object(i["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:e,sourceNamespace:t,targetCluster:a,targetNamespace:n}})}function h(e){return Object(i["a"])({url:"/v1/pipeline/status",method:"post",data:e})}},"579f":function(e,t,a){},"641a":function(e,t,a){"use strict";a("11d1")},6762:function(e,t,a){"use strict";var i=a("5ca1"),n=a("c366")(!0);i(i.P,"Array",{includes:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"699c":function(e,t,a){},"75fc":function(e,t,a){"use strict";var i=a("a745"),n=a.n(i),o=a("db2a");function l(e){if(n()(e))return Object(o["a"])(e)}var r=a("67bb"),s=a.n(r),c=a("5d58"),p=a.n(c),u=a("774e"),d=a.n(u);function m(e){if("undefined"!==typeof s.a&&null!=e[p.a]||null!=e["@@iterator"])return d()(e)}var f=a("e630");function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(e){return l(e)||m(e)||Object(f["a"])(e)||v()}a.d(t,"a",(function(){return b}))},"768b":function(e,t,a){"use strict";var i=a("a745"),n=a.n(i);function o(e){if(n()(e))return e}var l=a("67bb"),r=a.n(l),s=a("5d58"),c=a.n(s);function p(e,t){var a=null==e?null:"undefined"!==typeof r.a&&e[c.a]||e["@@iterator"];if(null!=a){var i,n,o=[],l=!0,s=!1;try{for(a=a.call(e);!(l=(i=a.next()).done);l=!0)if(o.push(i.value),t&&o.length===t)break}catch(p){s=!0,n=p}finally{try{l||null==a["return"]||a["return"]()}finally{if(s)throw n}}return o}}var u=a("e630");function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){return o(e)||p(e,t)||Object(u["a"])(e,t)||d()}a.d(t,"a",(function(){return m}))},"837b":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{style:{fontSize:e.fontsize}},[e.pipeline.app?a("div",{staticClass:"expand-row-wrapper"},[a("div",{staticClass:"expand-row-item"},[a("label",[e._v("备注")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.remark||"--"))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("k8s集群")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.cluster))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("基础镜像")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.baseImage.substring(e.pipeline.baseImage.lastIndexOf("/")+1)))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("资源池调度")]),e._v(" "),a("span",[e._v("调度策略 ("+e._s(e.pipeline.schedule.strategy)+")")]),e._v(" "),a("span",[e._v("资源池 ("+e._s(e.pipeline.schedule.node)+")")])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("部署策略")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.deployStrategy))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("选项开关")]),e._v(" "),a("div",{staticStyle:{"padding-left":"10px",display:"inline-block"}},[a("el-checkbox-group",{model:{value:e.pipeOptions,callback:function(t){e.pipeOptions=t},expression:"pipeOptions"}},e._l(e.pipeOptionsAll,(function(t){return a("el-checkbox",{staticClass:"pipeline-option",style:{fontSize:e.fontsize},attrs:{label:t,size:"mini",disabled:""}})})),1)],1)]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("健康检测")]),e._v(" "),a("div",{staticStyle:{"padding-left":"10px",display:"inline-block"}},[a("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"启动检测",checked:e.pipeline.startupProbe.enable,size:"mini",disabled:""}}),e._v(" "),a("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"存活检查",checked:e.pipeline.livenessProbe.enable,size:"mini",disabled:""}}),e._v(" "),a("el-checkbox",{staticClass:"pipeline-option",attrs:{label:"就绪检测",checked:e.pipeline.readinessProbe.enable,size:"mini",disabled:""}})],1)]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("JVM参数")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.jvmOpts||"--"))])]),e._v(" "),e.pipeline.pvc.enable?a("div",{staticClass:"expand-row-item"},[a("label",[e._v("持久存储")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.pvc.enable?e.pipeline.pvc.name:""))])]):e._e(),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("关闭前保留时间")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.preStopRetainSeconds))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("关闭前回调地址")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.preStopWebhook||"--"))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("Eolinker任务数")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.eolinkerIDs&&e.pipeline.eolinkerIDs.length>0?e.pipeline.eolinkerIDs.length:"--"))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("Webhook")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.webhook.url||"--"))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("端口")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.ports.map((function(e){return e.value})).join(",")))])]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("自定义环境变量")]),e._v(" "),a("span",[e._l(e.pipeline.envs,(function(t,i){return["USER"===t.type?a("div",{staticStyle:{display:"inline-block","padding-right":"20px"}},[e._v("\n            "+e._s(t.name)+"="+e._s(t.value)+"\n          ")]):e._e()]}))],2)]),e._v(" "),a("div",{staticClass:"expand-row-item"},[a("label",[e._v("创建时间")]),e._v(" "),a("span",[e._v(e._s(e.pipeline.createdTime))])])]):a("div",[e._v("\n    数据丢失\n  ")])])},n=[],o=a("768b"),l=(a("ac6a"),a("ffc1"),a("b144")),r={props:{pipeline:{type:Object,default:function(){return{}},required:!0},fontsize:{type:String,default:"12px",required:!1}},mounted:function(){for(var e=0,t=Object.entries(this.pipeline.options);e<t.length;e++){var a=Object(o["a"])(t[e],2),i=a[0],n=a[1];this.pipeOptionsAll.push(Object(l["b"])(i)),n&&this.pipeOptions.push(Object(l["b"])(i))}this.pipeOptionsOrigin=Object(l["a"])(this.pipeOptions)},computed:{},data:function(){return{pipeOptionsOrigin:[],pipeOptions:[],pipeOptionsAll:[]}},methods:{}},s=r,c=(a("d152"),a("2877")),p=Object(c["a"])(s,i,n,!1,null,null,null);t["a"]=p.exports},9684:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container app-deploy-container",staticStyle:{margin:"0",padding:"0"},attrs:{"element-loading-text":"数据加载中"}},[a("menu-tabs",{attrs:{"tab-name":"app-deploy"}}),e._v(" "),a("maintain-alert",{attrs:{"maintain-type":"cd"}}),e._v(" "),a("app-selector2",{attrs:{"show-detail":!0,"update-history":!0},on:{change:e.changeCurrApp}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:this.currApp,expression:"this.currApp"}],staticStyle:{"min-height":"180px",margin:"10px"}},[a("job-runner-alert",{ref:"jobRunnerAlert",attrs:{app:this.currApp,"job-type":"CD"}}),e._v(" "),a("div",{staticStyle:{"padding-top":"10px"}},[a("div",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-position"},on:{click:e.batchDeploy}},[e._v("批量发布")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:e.createPage}},[e._v("新建流程")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text",icon:"el-icon-price-tag"},on:{click:e.imageBuildPage}},[e._v("构建镜像")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-document"},on:{click:e.offlineAppDialog}},[e._v("服务下线说明")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"10px",color:"#bbb"},attrs:{type:"text",icon:"el-icon-price-console"},on:{click:e.multiClusterPodExecPage}},[a("svg-icon",{attrs:{"icon-class":"console"}}),e._v("\n          批量进入第一个容器\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"同时进入所勾选发布流程下的第一个Pod容器里"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),e._v(" "),a("el-button",{staticStyle:{"margin-left":"30px",color:"#E6A23C"},attrs:{type:"text",icon:"el-icon-position"},on:{click:e.urgentDeploy}},[e._v("紧急发布")])],1),e._v(" "),a("div",{staticStyle:{float:"right"}},[a("export-button",{attrs:{"table-ref":this.$refs.pipelineTable}}),e._v(" "),a("el-dropdown",{attrs:{trigger:"click","hide-on-click":!1}},[a("span",{staticClass:"el-dropdown-link"},[e._v("\n            显示更多"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",[a("el-checkbox",{model:{value:e.tableColumnsShowMore.baseImage,callback:function(t){e.$set(e.tableColumnsShowMore,"baseImage",t)},expression:"tableColumnsShowMore.baseImage"}},[e._v("基础镜像")])],1),e._v(" "),a("el-dropdown-item",[a("el-checkbox",{model:{value:e.tableColumnsShowMore.createTime,callback:function(t){e.$set(e.tableColumnsShowMore,"createTime",t)},expression:"tableColumnsShowMore.createTime"}},[e._v("创建时间")])],1),e._v(" "),a("el-dropdown-item",[a("el-checkbox",{model:{value:e.tableColumnsShowMore.jvmOpts,callback:function(t){e.$set(e.tableColumnsShowMore,"jvmOpts",t)},expression:"tableColumnsShowMore.jvmOpts"}},[e._v("JVM参数")])],1)],1)],1),e._v(" "),a("span",{staticStyle:{"font-size":"12px",color:"#E6A23C","font-weight":"bold"}},[e._v("状态筛选：")]),e._v(" "),a("el-select",{staticStyle:{width:"120px","margin-right":"20px"},attrs:{size:"small"},on:{change:e.pipelineFilter},model:{value:e.statusFilter,callback:function(t){e.statusFilter=t},expression:"statusFilter"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),e._v(" "),a("el-radio-group",{attrs:{size:"mini",fill:"#909399"},on:{input:e.pipelineFilter},model:{value:e.cloudCategoryCurr,callback:function(t){e.cloudCategoryCurr=t},expression:"cloudCategoryCurr"}},[a("el-radio-button",{attrs:{label:"_all_"}},[e._v("所有\n            "),a("span",{staticStyle:{color:"#fff","border-radius":"10px",display:"inline-block","line-height":"1.4em",width:"20px"}},[e._v("\n              \n            ")])]),e._v(" "),e._l(e.cloudCategories,(function(t){return a("el-radio-button",{attrs:{label:t.name}},[e._v(e._s(t.desc)+"\n            "),a("span",{staticStyle:{color:"#fff","background-color":"#409eff","border-radius":"10px",display:"inline-block","line-height":"1.4em",width:"20px"}},[e._v("\n              "+e._s(t.pipelineCount)+"\n            ")])])}))],2)],1),e._v(" "),a("div",{staticStyle:{clear:"both"}})]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"pipelineTable",attrs:{data:e.tableData,"highlight-selection-row":!0,"element-loading-text":"数据加载中...",border:"",fit:"","cell-style":e.cellStyle}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"40"}}),e._v(" "),a("el-table-column",{attrs:{type:"expand",label:"详情",width:"50"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("pipeline-expand",{attrs:{pipeline:e.row}})]}}])}),e._v(" "),a("el-table-column",{attrs:{type:"index",width:"40"}}),e._v(" "),a("el-table-column",{attrs:{label:"状态",width:"70",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["enabled"===t.row.status?a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"success",size:"mini"}},[e._v("\n            "+e._s(e.convertStatus(t.row.status))+"\n          ")]):a("el-tag",{staticStyle:{"font-weight":"bold"},attrs:{type:"warning",size:"small"}},[e._v("\n            "+e._s(e.convertStatus(t.row.status))+"\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:e.cicdOneKey?"290px":"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"12px"}},[a("router-link",{attrs:{to:{name:"pod-index",query:{cluster:t.row.cluster,namespace:t.row.namespace,app:t.row.app}},target:"_blank"}},[a("i",{staticClass:"el-icon-menu",staticStyle:{color:"#409EFF","font-weight":"500"}},[e._v("实例")])]),e._v(" "),a("router-link",{attrs:{to:{name:"app-pipeline-edit",query:{pipelineId:t.row.id}},target:"_blank"}},[a("i",{staticClass:"el-icon-edit",staticStyle:{color:"#409EFF","font-weight":"500"}},[e._v("编辑")])]),e._v(" "),a("el-dropdown",{on:{command:e.manageDropdownCommand}},[a("span",{staticClass:"el-dropdown-link",staticStyle:{"font-size":"12px","margin-left":"5px"}},[e._v("\n                更多"),a("i",{staticClass:"el-icon-arrow-down "})]),e._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-link",command:"showAddr##"+t.row.id}},[e._v("查看访问地址")]),e._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-position",command:"deployHistory##"+t.row.id}},[e._v("发布历史")]),e._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-tickets",command:"bugfixBranch##"+t.row.id}},[e._v("创建Bugfix分支")]),e._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-tickets",command:"appLog##"+t.row.id}},[e._v("Logback日志")]),e._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-copy-document",command:"clone##"+t.row.id}},[e._v("克隆")]),e._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-document-copy",command:"syncConfigToOther##"+t.row.id}},[e._v("配置同步到其他流程")]),e._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-document-copy",command:"k8sDeployment##"+t.row.id}},[e._v("k8s部署配置")]),e._v(" "),a("el-dropdown-item",{attrs:{icon:"el-icon-setting",command:"updateStatus##"+t.row.id}},[e._v("修改状态")]),e._v(" "),a("el-dropdown-item",{staticStyle:{color:"orange"},attrs:{icon:"el-icon-scissors",command:"remove##"+t.row.id}},[e._v("下线")])],1)],1),e._v(" "),a("el-tooltip",{attrs:{effect:"dark",content:"同时执行【镜像构建】和【服务发布】",placement:"top"}},[e.cicdOneKey?a("el-button",{staticStyle:{padding:"5px 7px"},attrs:{type:"success",size:"mini",disabled:"enabled"!==t.row.status},on:{click:function(a){return e.buildAndDeployDialog(t.row)}}},[e._v("\n                构建+发布\n              ")]):e._e()],1),e._v(" "),a("el-button",{staticClass:"el-icon-position",staticStyle:{padding:"5px 7px"},attrs:{type:"primary",size:"mini",disabled:"enabled"!==t.row.status},on:{click:function(a){return e.showDeployDialog([t.row])}}},[e._v("发布\n            ")])],1),e._v(" "),a("div",[e._l(t.row.extraAttr.clusterLabels,(function(e){return a("el-alert",{key:e,staticClass:"env-label-alert",attrs:{title:e,type:"info","show-icon":!0,closable:!1}})})),e._v(" "),"fstest-metadata"===t.row.namespace?a("el-alert",{staticClass:"env-label-alert",attrs:{title:"元数据专用环境,咨询李磊",type:"info","show-icon":!0,closable:!1}}):e._e(),e._v(" "),t.row.extraAttr.deregisterPodSize>0?a("el-alert",{staticClass:"env-label-alert",attrs:{title:"摘除了"+t.row.extraAttr.deregisterPodSize+"个pod，不受发版影响",type:"info","show-icon":!0,closable:!1}}):e._e()],2)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行环境",prop:"namespace",width:"160"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n          运行环境\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("template",{slot:"content"},[a("div",[e._v("第一行为运行环境名，第二行为k8s集群名")])]),e._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)]}},{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-weight":"bold"}},[e._v("\n            "+e._s(t.row.namespace)+"\n          ")]),e._v(" "),a("div",{staticStyle:{"font-weight":"normal",color:"#666","font-size":"10px","margin-top":"-8px"}},[e._v("\n            "+e._s(t.row.cluster)+"\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"",width:"180"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n          云环境\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("template",{slot:"content"},[a("div",[e._v("第一行为云环境名，第二行为云类型")]),e._v(" "),a("hr"),e._v(" "),a("div",[e._v("云类型分为：")]),e._v(" "),a("p",[e._v("纷享云：部署在纷享机房")]),e._v(" "),a("p",[e._v("公有云：部署在纷享购买的公有云平台（阿里云、华为云、腾讯云、AWS）")]),e._v(" "),a("p",[e._v("专属云：部署在客户购买的三大公有云平台（阿里云、华为云、腾讯云）")]),e._v(" "),a("p",[e._v("混合云：部署在客户自建机房")])]),e._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)]}},{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-weight":"bold"}},[a("div",[e._v(e._s(t.row.extraAttr.clusterSummary))]),e._v(" "),a("div",[t.row.extraAttr&&t.row.extraAttr.cloudCategoryDesc?a("div",{staticStyle:{"font-weight":"normal",color:"#666","font-size":"10px","margin-top":"-8px"}},[e._v("\n                "+e._s(t.row.extraAttr.cloudCategoryDesc)+"\n              ")]):e._e()])])]}}])}),e._v(" "),e.tableColumnsShowMore.baseImage?a("el-table-column",{attrs:{label:"基础镜像","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.baseImage.substring(t.row.baseImage.lastIndexOf("/")+1)))])]}}],null,!1,3927449695)}):e._e(),e._v(" "),e.tableColumnsShowMore.createTime?a("el-table-column",{attrs:{label:"创建时间","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.createdTime))])]}}],null,!1,830325747)}):e._e(),e._v(" "),e.tableColumnsShowMore.jvmOpts?a("el-table-column",{attrs:{label:"JVM参数","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.jvmOpts))])]}}],null,!1,2975852911)}):e._e(),e._v(" "),a("el-table-column",{attrs:{label:"部署模块",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.appModules.length)+"\n          "),a("el-popover",{attrs:{placement:"right",width:"680",align:"center",trigger:"click"}},[a("el-table",{staticStyle:{"max-height":"460px","overflow-y":"auto"},attrs:{data:t.row.appModules}},[a("el-table-column",{attrs:{type:"index",width:"40"}}),e._v(" "),a("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址"}}),e._v(" "),a("el-table-column",{attrs:{prop:"module",label:"子模块"}}),e._v(" "),a("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}})],1),e._v(" "),a("el-button",{staticStyle:{"margin-left":"5px","font-size":"12px"},attrs:{slot:"reference",type:"text"},slot:"reference"},[e._v("查看")])],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"运行版本",prop:"extraAttr.deployTag","show-overflow-tooltip":"","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.extraAttr.deployModules?a("div",{staticStyle:{display:"inline-block","margin-right":"5px"}},[a("el-popover",{attrs:{placement:"left",width:"760",trigger:"click"}},[a("el-table",{staticStyle:{"max-height":"480px","overflow-y":"auto"},attrs:{data:t.row.extraAttr.deployModules}},[a("el-table-column",{attrs:{type:"index",width:"40"}}),e._v(" "),a("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址"}}),e._v(" "),a("el-table-column",{attrs:{prop:"module",label:"子模块"}}),e._v(" "),a("el-table-column",{attrs:{prop:"tag",label:"代码分支/标签"}}),e._v(" "),a("el-table-column",{attrs:{prop:"commitIdShort",label:"提交ID"}}),e._v(" "),a("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}})],1),e._v(" "),a("el-button",{staticStyle:{margin:"0",padding:"0","font-size":"12px"},attrs:{slot:"reference",type:"text"},slot:"reference"},[e._v("详情")])],1)],1):e._e(),e._v(" "),null===t.row.extraAttr.deployTag?a("span",[a("i",{staticClass:"el-icon-loading"})]):a("span",[e._v(e._s(t.row.extraAttr.deployTag))]),e._v(" "),t.row.extraAttr.deployStatus?a("div",{staticStyle:{width:"100%","background-color":"rgb(236 140 61)","font-size":"12px",color:"white","font-weight":"bold","line-height":"2em",height:"2em"}},[a("i",{staticClass:"el-icon-loading"}),e._v("\n            "+e._s(t.row.extraAttr.deployStatus)+"\n            "),a("router-link",{staticStyle:{"text-decoration":"underline","margin-left":"3px"},attrs:{to:{name:"cicd-app-deploy-detail",query:{jobId:t.row.extraAttr.deployJobId}},target:"_blank"}},[e._v("\n              [进入详情页]\n            ")])],1):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"最近发布",width:"110","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"10px","line-height":"1.8em",overflow:"hidden"}},[t.row.extraAttr.lastDeployTime?a("div",[e._v(e._s(t.row.extraAttr.lastDeployTime))]):e._e(),e._v(" "),t.row.extraAttr.lastDeployUser?a("div",[e._v("发布人："+e._s(t.row.extraAttr.lastDeployUser))]):e._e()])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"",width:"110"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n          扩缩容\n          "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("template",{slot:"content"},[a("p",[e._v("扩缩容类型：最低副本数 → 最大副本数")]),e._v(" "),a("p",[e._v("比如：")]),e._v(" "),a("hr"),e._v(" "),a("p",[e._v("自动: 4 → 12 （配置了自动扩缩容，且可扩容的最大副本数为12）")]),e._v(" "),a("p",[e._v("定时: 4 → 8 （配置了定时扩缩容，且可扩容的最大副本数为8）")])]),e._v(" "),a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],2)]}},{key:"default",fn:function(t){return[a("div",{staticStyle:{"font-size":"10px","line-height":"1.8em",overflow:"hidden"}},[null===t.row.extraAttr.autoScaleV2MaxReplicas?a("div",[a("i",{staticClass:"el-icon-loading"})]):a("div",["-"!==t.row.extraAttr.autoScaleV2MaxReplicas?a("div",{staticStyle:{position:"relative"}},[a("b",[e._v("自动:")]),e._v("\n                "+e._s(t.row.extraAttr.autoScaleV2MinReplicas)+" → "+e._s(t.row.extraAttr.autoScaleV2MaxReplicas)+"\n                "),a("el-button",{staticStyle:{"font-size":"10px",padding:"0",margin:"0"},attrs:{type:"text"},on:{click:function(a){return e.scalePage(t.row,"autoscalev2")}}},[e._v("查看")])],1):e._e(),e._v(" "),"-"!==t.row.extraAttr.cronScaleMaxReplicas?a("div",[a("b",[e._v("定时:")]),e._v("\n                "+e._s(t.row.extraAttr.cronScaleMinReplicas)+" → "+e._s(t.row.extraAttr.cronScaleMaxReplicas)+"\n                "),a("el-button",{staticStyle:{"font-size":"10px",padding:"0",margin:"0"},attrs:{type:"text"},on:{click:function(a){return e.scalePage(t.row,"cronscale")}}},[e._v("查看")])],1):e._e()])])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"实例（Pod)",align:"center"}},[a("el-table-column",{attrs:{width:"80",align:"center",property:"replicas"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n            配置数\n            "),a("el-tooltip",{attrs:{effect:"dark",content:"发布流程里配置的实例数",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{width:"80",align:"center",label:"运行数"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n            运行数\n            "),a("el-tooltip",{attrs:{effect:"dark",content:"当前运行的实例数",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(t){return[null===t.row.extraAttr.runningPodNum?a("span",[a("i",{staticClass:"el-icon-loading"})]):a("span",[e._v(e._s(t.row.extraAttr.runningPodNum))]),e._v(" "),t.row.extraAttr.runningPodNum>0?a("el-button",{staticStyle:{"margin-left":"5px","font-size":"12px"},attrs:{slot:"reference",type:"text"},on:{click:function(a){return e.showPodTable(t.row)}},slot:"reference"},[e._v("\n              查看\n            ")]):e._e()]}}])})],1),e._v(" "),a("el-table-column",{attrs:{label:"CPU",width:"90",prop:"resources.limitCPU"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n          CPU\n          "),a("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(t){return t.row.resources?[e._v("\n          "+e._s(t.row.resources.requestCPU.toFixed(1))+" - "+e._s(t.row.resources.limitCPU.toFixed(1))+"\n        ")]:void 0}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{width:"110px",prop:"resources.limitMemory"},scopedSlots:e._u([{key:"header",fn:function(t){return[e._v("\n          内存 (MB)\n          "),a("el-tooltip",{attrs:{effect:"dark",content:"最低要求值 - 最大可用值",placement:"top"}},[a("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(t){return t.row.resources?[e._v("\n          "+e._s(t.row.resources.requestMemory)+" - "+e._s(t.row.resources.limitMemory)+"\n        ")]:void 0}}],null,!0)})],1)],1),e._v(" "),a("div",{staticStyle:{margin:"10px"}},[a("pipeline-doc")],1),e._v(" "),a("el-dialog",{staticClass:"deploy-dialog",attrs:{visible:e.deployData.dialogVisible,"close-on-click-modal":!1,top:"5vh",width:"960px"},on:{"update:visible":function(t){return e.$set(e.deployData,"dialogVisible",t)}}},[a("template",{slot:"title"},[e._v("\n      应用发布 "),a("small",{staticStyle:{"padding-left":"20px",color:"#666"}},[e._v("（默认会选择当前运行的版本号）")])]),e._v(" "),a("el-form",{ref:"dialogDeployForm",attrs:{model:e.deployData.form,"label-width":"100px"}},[e._l(e.deployData.imageOptions,(function(t,i){return a("div",[a("el-form-item",{staticClass:"module-git-url",attrs:{label:""}},[a("span",{staticStyle:{color:"#b4532a","font-weight":"bold"}},[e._v("\n            Git地址---模块： "+e._s(t.gitUrl)+" --- "+e._s(t.gitModule?t.gitModule:"[空]")+"\n          ")])]),e._v(" "),a("el-form-item",{attrs:{label:"镜像版本"}},[a("div",{staticStyle:{display:"inline-block",width:"800px"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},on:{change:e.imageTagChange},model:{value:t.imageSelected,callback:function(a){e.$set(t,"imageSelected",a)},expression:"item.imageSelected"}},[a("el-option",{attrs:{value:""}},[a("div",{staticStyle:{"font-weight":"bold",color:"#888","font-size":"12px"}},[a("span",{staticStyle:{display:"inline-block",width:"50%"}},[e._v("版本")]),e._v(" "),a("span",{staticStyle:{display:"inline-block",width:"10%"}},[e._v("父POM")]),e._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[e._v("创建时间")]),e._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[e._v("备注")])])]),e._v(" "),e._l(t.images,(function(t){return a("el-option",{key:t.name,attrs:{label:t.tag+"          (父pom: "+t.parentPomShortName+")",value:t.name}},[a("div",[a("span",{staticStyle:{display:"inline-block",width:"50%"}},[a("b",[e._v(e._s(t.tag))])]),e._v(" "),a("span",{staticStyle:{display:"inline-block",width:"10%"}},[e._v(e._s(t.parentPomShortName))]),e._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[e._v(e._s(t.createTime))]),e._v(" "),a("span",{staticStyle:{display:"inline-block",width:"20%"}},[e._v(e._s(t.remark?t.remark.length>20?t.remark.substring(0,20)+"...":t.remark:""))])])])})),e._v(" "),t.images?e._e():a("div",{staticStyle:{color:"#888","text-align":"center","font-size":"13px",margin:"10px"}},[e._v("\n                无可用的镜像， 去\n                "),a("el-button",{staticStyle:{"font-size":"13px"},attrs:{type:"text"},on:{click:e.imageBuildPage}},[e._v("构建镜像")])],1)],2)],1)])],1)})),e._v(" "),a("el-form-item",{attrs:{label:"每批升级数"}},[a("div",{staticStyle:{display:"inline-block",width:"800px"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.deployData.form.maxSurge,callback:function(t){e.$set(e.deployData.form,"maxSurge",t)},expression:"deployData.form.maxSurge"}},[a("el-option-group",{attrs:{label:"按百分比"}},[a("el-option",{attrs:{label:"100%",value:"100%"}}),e._v(" "),a("el-option",{attrs:{label:"50%",value:"50%"}}),e._v(" "),a("el-option",{attrs:{label:"25%",value:"25%"}})],1),e._v(" "),a("el-option-group",{attrs:{label:"按个数"}},[a("el-option",{attrs:{label:"3",value:"3"}}),e._v(" "),a("el-option",{attrs:{label:"2",value:"2"}}),e._v(" "),a("el-option",{attrs:{label:"1",value:"1"}})],1)],1)],1)]),e._v(" "),a("el-form-item",{attrs:{label:"发布描述"}},[a("div",{staticStyle:{display:"inline-block",width:"800px"}},[a("el-input",{attrs:{type:"textarea",rows:2,maxlength:256,max:256},model:{value:e.deployData.form.remark,callback:function(t){e.$set(e.deployData.form,"remark",t)},expression:"deployData.form.remark"}})],1)]),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"发布环境"}},[e._l(this.deployData.pipelines,(function(t){return a("el-tag",{staticStyle:{"font-weight":"bold","margin-right":"8px","font-size":"14px"},attrs:{effect:"plain"}},[e._v("\n          "+e._s(t.namespace)+" ("+e._s(t.cluster)+")\n        ")])})),e._v(" "),this.deployData.pipelines.length>1?a("div",{staticStyle:{color:"#888","line-height":"1.3em"}},[e._v("\n          提示： 批量发布时，会首先发布第一个环境，等发布成功后再同时并行发布所有剩余环境\n        ")]):e._e(),e._v(" "),this.deployData.pipelines.filter((function(e){return"forceecrm-public-prod"===e.namespace})).length>0?a("div",{staticStyle:{"line-height":"1.3em",color:"#f3794d"}},[e._v("\n          提示： 复制云环境的发布，只能选择复制云专用父POM所构建出来的镜像\n        ")]):e._e()],2),e._v(" "),a("el-form-item",[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"执行Eolinker接口测试"},model:{value:e.deployData.form.eolinkerTest,callback:function(t){e.$set(e.deployData.form,"eolinkerTest",t)},expression:"deployData.form.eolinkerTest"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n            是否执行Eolinker接口测试？如果发布流程没有配置Eolinker接口测试，则该选项不会产生任何作用"),a("br"),e._v("\n            大版本发布期间，建议关闭Eolinker接口测试可以提升发布效率，避免因接口测试导致发布失败。\n          ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1)],2),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticStyle:{"font-size":"13px","margin-right":"20px"},attrs:{type:"text"},on:{click:e.imageBuildPage}},[e._v("去构建镜像")]),e._v(" "),a("el-button",{on:{click:function(t){e.deployData.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.deployData.dialogLoading,expression:"deployData.dialogLoading"}],attrs:{type:"primary"},on:{click:e.deploySubmit}},[e._v("发 布")])],1)],2),e._v(" "),a("el-dialog",{attrs:{title:"发布流程复制",visible:e.dialogCloneVisible,width:"40%","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogCloneVisible=t}}},[a("el-form",{ref:"dialogCloneForm",attrs:{model:e.dialogCloneForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"应用名",prop:"app"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.dialogCloneForm.app,callback:function(t){e.$set(e.dialogCloneForm,"app","string"===typeof t?t.trim():t)},expression:"dialogCloneForm.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"源环境"}},[a("el-row",[a("el-col",{staticStyle:{"padding-right":"20px"},attrs:{span:12}},[a("el-form-item",{attrs:{prop:"cluster"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择k8s集群",disabled:""},model:{value:e.dialogCloneForm.sourceCluster,callback:function(t){e.$set(e.dialogCloneForm,"sourceCluster",t)},expression:"dialogCloneForm.sourceCluster"}},e._l(e.dialogCloneForm.clusterOptions,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name+" ("+e.description+")",value:e.name}})})),1)],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{prop:"namespace"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择运行环境",disabled:""},model:{value:e.dialogCloneForm.sourceNamespace,callback:function(t){e.$set(e.dialogCloneForm,"sourceNamespace",t)},expression:"dialogCloneForm.sourceNamespace"}},e._l(e.dialogCloneForm.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1)],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"目标环境"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{prop:"namespace"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",placeholder:"选择运行环境"},model:{value:e.dialogCloneForm.targetNamespaces,callback:function(t){e.$set(e.dialogCloneForm,"targetNamespaces",t)},expression:"dialogCloneForm.targetNamespaces"}},e._l(e.dialogCloneForm.namespaceOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("div",{staticStyle:{color:"#888","margin-top":"-10px","font-size":"12px"}},[e._v("备注：每个环境都会打开一个发布流程新建页面，每个都需要人工进行配置确认和提交")])],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogCloneVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.clonePipeline()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"实例列表",visible:e.dialogPodTableVisible,width:"60%"},on:{"update:visible":function(t){e.dialogPodTableVisible=t}}},[a("pod-simple-table",{attrs:{cluster:e.dialogPodTable.cluster,namespace:e.dialogPodTable.namespace,app:e.dialogPodTable.app}})],1),e._v(" "),a("el-dialog",{attrs:{title:"k8s deployment 信息",visible:e.dialogK8sDeploymentVisible,width:"60%"},on:{"update:visible":function(t){e.dialogK8sDeploymentVisible=t}}},[a("deployment-detail",{attrs:{cluster:e.dialogK8sDeployment.cluster,namespace:e.dialogK8sDeployment.namespace,app:e.dialogK8sDeployment.app}})],1),e._v(" "),a("el-dialog",{attrs:{title:"创建Bugfix开发分支",visible:e.bugfixBranchVisible,width:"60%"},on:{"update:visible":function(t){e.bugfixBranchVisible=t}}},[a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-alert",{attrs:{title:"提示说明",type:"info","show-icon":"",description:"创建Bugfix分支时，会以当前环境的版本为基础，创建一个新的Git分支。然后大家在此分支上进行代码修复、提交和发布，避免误把其他环境的代码引入到当前环境。",effect:"dark"}})],1),e._v(" "),a("el-form",{attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"应用"}},[a("el-input",{attrs:{disabled:""},model:{value:e.bugfixBranch.app,callback:function(t){e.$set(e.bugfixBranch,"app",t)},expression:"bugfixBranch.app"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"集群"}},[a("el-input",{attrs:{disabled:""},model:{value:e.bugfixBranch.cluster,callback:function(t){e.$set(e.bugfixBranch,"cluster",t)},expression:"bugfixBranch.cluster"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-input",{attrs:{disabled:""},model:{value:e.bugfixBranch.namespace,callback:function(t){e.$set(e.bugfixBranch,"namespace",t)},expression:"bugfixBranch.namespace"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"Git地址"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择部署模块的Git地址"},model:{value:e.bugfixBranch.gitUrl,callback:function(t){e.$set(e.bugfixBranch,"gitUrl",t)},expression:"bugfixBranch.gitUrl"}},e._l(e.bugfixBranch.gitUrlOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.bugfixBranchVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.createBugfixBranch()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"确定要下线发布流程吗？",visible:e.removePipeDialogVisible,width:"30%"},on:{"update:visible":function(t){e.removePipeDialogVisible=t}}},[a("p",[e._v("应用："),a("b",[e._v(e._s(this.removePipeDialogData.app))])]),e._v(" "),a("p",[e._v("环境："),a("b",[e._v(e._s(this.removePipeDialogData.namespace)+" ("+e._s(this.removePipeDialogData.cluster)+")")])]),e._v(" "),a("p",{staticStyle:{color:"orangered"}},[e._v("提示：")]),e._v(" "),a("div",{staticStyle:{"padding-left":"10px",color:"orangered"}},[a("p",[a("b",[e._v("1. 只有运行副本数为0，才能正常下线")])]),e._v(" "),a("p",[a("b",[e._v("2. 下线后，会删除掉发布流程")])]),e._v(" "),a("p",[a("b",[e._v("3. 当前操作需要系统管理员权限，业务同学请按照页面中的【服务下线说明】的进行操作。")])])]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.removePipeDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.removePipe()}}},[e._v("继续下线")])],1)]),e._v(" "),a("el-dialog",{attrs:{title:"服务下线说明",visible:e.offlineAppDialogVisible,width:"30%"},on:{"update:visible":function(t){e.offlineAppDialogVisible=t}}},[a("div",{staticStyle:{"line-height":"2em"}},[e._v("\n      如果服务已不再使用，请申请下线，避免资源浪费。下线方式： 通过CRM系统的【k8s应用下线申请】对象进行申请。"),a("br"),e._v("\n      对象地址：\n      "),a("a",{staticStyle:{color:"#409EFF","text-decoration":"underline"},attrs:{href:"https://www.fxiaoke.com/XV/UI/Home#crm/list/=/k8s_application_offline_a__c",target:"_blank"}},[e._v("\n        https://www.fxiaoke.com/XV/UI/Home#crm/list/=/k8s_application_offline_a__c\n      ")])])]),e._v(" "),a("el-dialog",{attrs:{title:"确定要修改发布流程状态吗？",visible:e.updateStatusDialogVisible,width:"30%"},on:{"update:visible":function(t){e.updateStatusDialogVisible=t}}},[a("p",[e._v("应用："),a("b",[e._v(e._s(this.updateStatusDialogData.app))])]),e._v(" "),a("p",[e._v("环境："),a("b",[e._v(e._s(this.updateStatusDialogData.namespace)+" ("+e._s(this.updateStatusDialogData.cluster)+")")])]),e._v(" "),a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.updateStatusDialogData.status,callback:function(t){e.$set(e.updateStatusDialogData,"status",t)},expression:"updateStatusDialogData.status"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.updateStatusDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.updateStatus()}}},[e._v("确认")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"构建+发布",visible:e.buildAndDeployDialogVisible,width:"960px",top:"2vh"},on:{"update:visible":function(t){e.buildAndDeployDialogVisible=t}}},[a("div",{staticStyle:{margin:"-40px -20px -20px"}},[a("build-and-deploy",{attrs:{"pipeline-id":e.buildAndDeployPipelineId},on:{successHandler:function(t){e.buildAndDeployDialogVisible=!1}}})],1)]),e._v(" "),a("el-drawer",{attrs:{withHeader:!1,"destroy-on-close":!0,visible:e.addressVisible,direction:"btt",size:"400px"},on:{"update:visible":function(t){e.addressVisible=t}}},[a("app-address",{attrs:{cluster:e.address.cluster,namespace:e.address.namespace,app:e.address.app,icon:"el-icon-link"}})],1)],1)},n=[],o=(a("28a5"),a("a481"),a("6762"),a("2fdb"),a("7f7f"),a("2d63")),l=a("51a9"),r=a("b144"),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pod-simple-table-wrapper"},[a("div",{staticStyle:{"margin-top":"-30px","text-align":"center"}},[a("b",[e._v("应用：")]),e._v(" "+e._s(this.app)+"\n    "),a("b",{staticStyle:{"margin-left":"20px"}},[e._v("环境：")]),e._v(" "+e._s(this.namespace)+" ("+e._s(this.cluster)+")\n  ")]),e._v(" "),a("div",{staticStyle:{"text-align":"right","padding-right":"20px"}},[a("el-button",{staticClass:"el-icon-menu",attrs:{type:"text"},on:{click:function(t){return e.podPage()}}},[e._v("实例管理页\n    ")]),e._v(" "),a("el-button",{staticClass:"el-icon-bank-card",staticStyle:{"margin-left":"20px"},attrs:{type:"text"},on:{click:function(t){return e.podsShell()}}},[e._v("进入所有容器\n    ")])],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.pods,border:"",fit:"","highlight-current-row":"","row-key":"name"}},[a("el-table-column",{attrs:{label:"实例名",sortable:"",prop:"name"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{style:{color:t.row.ready?"#67C23A":"#F56C6C"}},[e._v(e._s(t.row.name))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"Pod IP",width:"120",sortable:"",prop:"podIP"}}),e._v(" "),a("el-table-column",{attrs:{label:"Host IP",width:"120",sortable:"",prop:"hostIP"}}),e._v(" "),a("el-table-column",{attrs:{label:"重启次数",width:"110",align:"center",sortable:"",prop:"restartCount"}}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",sortable:"",prop:"createTime",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"180",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.showStdoutLogDialog(t.row)}}},[e._v("标准输出\n        ")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.webShellPage(t.row.name)}}},[e._v("进入容器\n        ")])]}}])})],1),e._v(" "),a("el-dialog",{attrs:{title:"容器启动日志（标准输出)",visible:e.podStdoutVisible,top:"5vh","close-on-click-modal":!1,width:"70%","append-to-body":"",center:""},on:{"update:visible":function(t){e.podStdoutVisible=t}}},[a("div",{staticStyle:{"margin-top":"-30px"}},[a("pod-stdout",{attrs:{cluster:this.podStdout.cluster,namespace:this.podStdout.namespace,pod:this.podStdout.pod,containers:this.podStdout.containers}})],1)])],1)},c=[],p=a("75fc"),u=a("a527"),d=a("cf89"),m={name:"PodSimpleTable",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},app:{type:String,required:!0}},components:{podStdout:d["a"]},data:function(){return{tableLoading:!1,pods:[],podStdoutVisible:!1,podStdout:{cluster:"",namespace:"",pod:"",containers:[]}}},watch:{cluster:function(e){this.loadPods()},namespace:function(e){this.loadPods()},app:function(e){this.loadPods()}},computed:{},mounted:function(){this.loadPods()},beforeDestroy:function(){},methods:{loadPods:function(){var e=this;this.tableLoading=!0,console.log("load pipeline pods"),Object(u["h"])(this.cluster,this.namespace,this.app).then((function(t){e.pods=t.data})).catch((function(t){e.$message.error("加载实例信息失败: "+t.message)})).finally((function(){e.tableLoading=!1}))},podsShell:function(){this.pods&&this.pods.length>0&&this.webShellPage(this.pods.map((function(e){return e.name})).join(","))},podPage:function(){var e=this.$router.resolve({name:"pod-index",query:{cluster:this.cluster,namespace:this.namespace,app:this.app}});window.open(e.href,"_blank")},showStdoutLogDialog:function(e){this.podStdoutVisible=!0,this.podStdout.cluster=e.cluster,this.podStdout.namespace=e.namespace,this.podStdout.pod=e.name,this.podStdout.containers=[e.container0Name].concat(Object(p["a"])(e.initContainersName))},webShellPage:function(e){var t="/api/page/redirect?type=webShell&cluster=".concat(this.cluster,"&namespace=").concat(this.namespace,"&app=").concat(this.app,"&pods=").concat(e);window.open(t)}}},f=m,v=(a("343d"),a("2877")),b=Object(v["a"])(f,s,c,!1,null,null,null),g=b.exports,h=a("8504"),_=a("837b"),y=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"divider-blockquote qa",staticStyle:{"padding-top":"20px"}},[a("span",[e._v("FAQ (常见问题解答）")]),e._v(" "),a("el-divider"),e._v(" "),e._m(0)],1)])},x=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"font-size":"12px",color:"#777","max-width":"1200px"}},[a("p",[a("b",[e._v("Q: 如何创建发布流程")])]),e._v(" "),a("p",[e._v("A: 研发同学在页面新建发布流程，创建好以后发布流程状态为[待审核] → 联系 @魏贺 进行审批 → 使用发布流程进行发布")]),e._v(" "),a("p",[a("b",[e._v("Q: 发布流程下线包含哪些操作？")])]),e._v(" "),a("p",[e._v("A: 首先会把环境下的实例数缩容为0，然后再把发布流程删除掉。对于其他Meta信息，管理员后期会统一做清理")]),e._v(" "),a("p",[a("b",[e._v("Q: 应用实例需要访问外网怎么办？")])]),e._v(" "),a("p",[e._v("A: 出于安全考虑，默认情况下应用是无法访问外网的。如果需要访问外网，请按照\n        "),a("a",{staticStyle:{color:"#01AAED"},attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=353404601"}},[e._v("k8s应用外网访问-申请")]),e._v(" 文档进行处理\n      ")]),e._v(" "),a("p",[a("b",[e._v("Q: 如何获取k8s环境下应用访问外网的出口IP？")])]),e._v(" "),a("p",[e._v("A: 请咨询 @丰莹莹")]),e._v(" "),a("p",[a("b",[e._v("Q: 为什么会有 [运行实例数] > [配置实例数] 的情况？")])]),e._v(" "),a("p",[e._v("A: 服务进行了自动扩容或手动扩容")]),e._v(" "),a("p",[a("b",[e._v("Q: 日志中心搜不到服务任何日志？")])]),e._v(" "),a("p",[e._v("A: 如果应用日志需要被收集起来，请在发布流程里勾上 [接入ClickHouse日志] 选项，并且在工程里引入和配置我们的oss-metrics组件"),a("br"),e._v("\n        oss-metrics组件使用说明：http://git.firstshare.cn/JavaCommon/oss-metrics")])])}],w={props:{},mounted:function(){},computed:{},data:function(){return{}},methods:{}},S=w,k=(a("bfce"),Object(v["a"])(S,y,x,!1,null,"186d9122",null)),C=k.exports,D=a("03b6"),O=a("76fe"),P=a("71df"),j=a("bcbd"),A=a("84d4"),T=a("4ad4"),$=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"deployment-detail"},[a("vue-json-pretty",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{},attrs:{data:e.data}})],1)},F=[],I=a("d538"),M=a.n(I),V={components:{VueJsonPretty:M.a},props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},app:{type:String,required:!0}},created:function(){this.loadData()},watch:{cluster:function(e){this.loadData()},namespace:function(e){this.loadData()},app:function(e){this.loadData()}},computed:{},data:function(){return{loading:!0,data:{}}},methods:{loadData:function(){var e=this;this.loading=!0,Object(h["c"])(this.cluster,this.namespace,this.app).then((function(t){e.data=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.loading=!1}))}}},L=V,R=Object(v["a"])(L,$,F,!1,null,null,null),z=R.exports,q=a("b562"),B=a("bb0b"),N=a("1e42"),U=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.gitModuleLoading||e.pipelineLoading||e.gitTagLoading||e.submitLoading,expression:"gitModuleLoading || pipelineLoading || gitTagLoading || submitLoading"}],staticClass:"app-container build-and-deploy-container"},[a("div",{staticStyle:{"font-size":"15px","font-weight":"bold",margin:"-32px 110px 10px"}},[e._v(" | "+e._s(this.pipeline.app))]),e._v(" "),a("el-card",{staticClass:"box-card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-size":"16px"}},[e._v("构建配置")])]),e._v(" "),a("div",[a("el-form",{ref:"buildFormRef",attrs:{size:"small",model:e.buildForm,"label-width":"120px",rules:e.buildFormRules}},[e._l(e.buildForm.modules,(function(t,i){return a("div",{key:i},[a("el-form-item",{staticClass:"module-git-url",staticStyle:{padding:"0",margin:"0 0 -5px 0"},attrs:{label:""}},[a("span",{staticStyle:{"font-size":"12px",color:"#b4532a","font-weight":"bold"}},[e._v("\n            部署模块： "+e._s(t.gitUrl)+" --- "+e._s(t.module)+"\n          ")])]),e._v(" "),a("el-form-item",{attrs:{label:"版本号"}},[a("el-row",[a("el-col",{attrs:{span:14}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},model:{value:e.buildForm.modules[i].tag,callback:function(t){e.$set(e.buildForm.modules[i],"tag",t)},expression:"buildForm.modules[index].tag"}},[e.buildForm.modules[i].branchOptions.length>0?a("el-option-group",{attrs:{label:"Git分支"}},e._l(e.buildForm.modules[i].branchOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}},[a("span",[a("b",[e._v(e._s(t.name))])])])})),1):e._e(),e._v(" "),a("el-option-group",{attrs:{label:"GitTag (message)"}},e._l(e.buildForm.modules[i].tagOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}},[a("span",[a("b",[e._v(e._s(t.name))]),a("span",{staticStyle:{"font-size":"12px",color:"#888","padding-left":"5px"}},[e._v(e._s(t.message?" ("+t.message.substring(0,30)+")":""))])])])})),1)],1)],1),e._v(" "),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:9,offset:1}},[a("b",{staticStyle:{color:"#606266"}},[e._v("编译环境")]),e._v(" "),a("el-select",{staticStyle:{width:"230px"},attrs:{filterable:""},model:{value:e.buildForm.modules[i].mavenImage,callback:function(t){e.$set(e.buildForm.modules[i],"mavenImage",t)},expression:"buildForm.modules[index].mavenImage"}},e._l(e.mavenOptions,(function(e){return a("el-option",{key:e,attrs:{label:e.split("/").pop(),value:e}})})),1)],1)],1)],1)],1)})),e._v(" "),a("el-form-item",{attrs:{prop:"parentPom",label:"父POM"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.buildForm.parentPom,callback:function(t){e.$set(e.buildForm,"parentPom",t)},expression:"buildForm.parentPom"}},[a("parent-pom-options")],1)],1)]),e._v(" "),a("el-form-item",{attrs:{label:"备注信息"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-input",{attrs:{type:"textarea",rows:1,maxlength:32,max:32},model:{value:e.buildForm.remark,callback:function(t){e.$set(e.buildForm,"remark",t)},expression:"buildForm.remark"}})],1)]),e._v(" "),a("el-form-item",{attrs:{label:"构建选项"}},[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"如果镜像存在，则覆盖"},model:{value:e.buildForm.forceCodeCompile,callback:function(t){e.$set(e.buildForm,"forceCodeCompile",t)},expression:"buildForm.forceCodeCompile"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n              默认情况下，当同名镜像已经存在的时候，不允许直接构建镜像，需要选择覆盖。勾选此选项后，会强制重新编译代码并覆盖原有镜像。\n            ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2),e._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"执行单元测试"},model:{value:e.buildForm.unitTest,callback:function(t){e.$set(e.buildForm,"unitTest",t)},expression:"buildForm.unitTest"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n              代码在进行Maven编译时，是否执行单元测试\n            ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2),e._v(" "),a("el-checkbox",{staticStyle:{margin:"0 0 0 30px"},attrs:{label:"依赖包版本校验",disabled:""},model:{value:e.buildForm.dependencyCheck,callback:function(t){e.$set(e.buildForm,"dependencyCheck",t)},expression:"buildForm.dependencyCheck"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n              对工程的依赖Jar包进行两方面检测。1：同一jar包是否存在多个版本；2：某个jar包版本是否低于平台所要求的最低版本\n            ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"提示"}},[a("el-alert",{attrs:{closable:!1,type:"info"}},[a("template",{slot:"title"},[a("div",[a("b",[e._v("镜像版本号生成机制:")])]),e._v(" "),a("div",[e._v("\n                1. 如果版本号为tag，则直接使用该名称"),a("br"),e._v("\n                2. 如果版本号为分支，则添加时间后缀。比如 master 会被替换为 master--202406041130"),a("br"),e._v("\n                3. 如果版本号中包含了斜线 /，则将其替换为 ---。比如 version/910 会被替换为 version---910"),a("br")]),e._v(" "),e.buildForm.modules&&e.buildForm.modules.length>1?a("div",{staticStyle:{"margin-top":"10px",color:"orangered"}},[a("b",[e._v("提示:")]),e._v(" 受系统设计限制，在【构建+部署】场景下，若存在多个部署模块，模块镜像将按串行方式构建。\n              ")]):e._e()])],2)],1)],2)],1)]),e._v(" "),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-size":"16px"}},[e._v("发布配置")])]),e._v(" "),a("div",[a("el-form",{ref:"deployFormRef",attrs:{size:"small",model:e.deployForm,"label-width":"100px",rules:e.deployFormRules}},[a("el-form-item",{attrs:{label:"每批升级数",prop:"maxSurge"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.deployForm.maxSurge,callback:function(t){e.$set(e.deployForm,"maxSurge",t)},expression:"deployForm.maxSurge"}},[a("el-option-group",{attrs:{label:"按百分比"}},[a("el-option",{attrs:{label:"100%",value:"100%"}}),e._v(" "),a("el-option",{attrs:{label:"50%",value:"50%"}}),e._v(" "),a("el-option",{attrs:{label:"25%",value:"25%"}})],1),e._v(" "),a("el-option-group",{attrs:{label:"按个数"}},[a("el-option",{attrs:{label:"3",value:"3"}}),e._v(" "),a("el-option",{attrs:{label:"2",value:"2"}}),e._v(" "),a("el-option",{attrs:{label:"1",value:"1"}})],1)],1)],1)]),e._v(" "),a("el-form-item",{attrs:{label:"发布描述",prop:"remark"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("el-input",{attrs:{type:"textarea",rows:1,maxlength:256,max:256},model:{value:e.deployForm.remark,callback:function(t){e.$set(e.deployForm,"remark",t)},expression:"deployForm.remark"}})],1)]),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"发布环境"}},[a("div",{staticStyle:{display:"inline-block"}},[a("el-tag",{staticStyle:{"font-weight":"bold","margin-right":"8px","font-size":"14px"},attrs:{effect:"plain"}},[e._v("\n              "+e._s(this.pipeline.namespace)+" ("+e._s(this.pipeline.cluster)+")\n            ")])],1),e._v(" "),a("div",{staticStyle:{display:"inline-block","margin-left":"60px"}},[a("el-checkbox",{staticStyle:{margin:"0 0 0 0"},attrs:{label:"执行Eolinker接口测试"},model:{value:e.deployForm.eolinkerTest,callback:function(t){e.$set(e.deployForm,"eolinkerTest",t)},expression:"deployForm.eolinkerTest"}}),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"light",placement:"top"}},[a("template",{slot:"content"},[e._v("\n                是否执行Eolinker接口测试？如果发布流程没有配置Eolinker接口测试，则该选项不会产生任何作用"),a("br"),e._v("\n                大版本发布期间，建议关闭Eolinker接口测试可以提升发布效率，避免因接口测试导致发布失败。\n              ")]),e._v(" "),a("i",{staticClass:"el-icon-info"})],2)],1),e._v(" "),"forceecrm-public-prod"===this.pipeline.namespace?a("div",{staticStyle:{"line-height":"1.3em",color:"#f3794d"}},[e._v("\n            提示： 复制云环境的发布，只能选择复制云专用父POM所构建出来的镜像\n          ")]):e._e()])],1)],1)]),e._v(" "),a("div",{staticStyle:{"margin-top":"20px","text-align":"right"}},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.buildSubmit}},[e._v("构建 & 发布")])],1)],1)},E=[],H=a("768b"),J=(a("ffc1"),a("1c4c"),a("ac6a"),a("5df3"),a("4f7f"),a("c5f6"),a("6a4c")),K=a("a6ce"),G={name:"build-and-deploy",props:{pipelineId:{type:Number,required:!0}},watch:{pipelineId:function(e){this.loadGitModule(),this.loadPipeline()}},data:function(){return{pipelineLoading:!1,gitModuleLoading:!1,gitTagLoading:!1,submitLoading:!1,buildOptions:{versionOptions:{}},pipeline:{},buildForm:{app:"",modules:[],unitTest:!1,forceCodeCompile:!1,dependencyCheck:!0,parentPom:"",remark:""},buildFormRules:{parentPom:[{required:!0,message:"请选择父POM"}]},deployForm:{pipelineIds:[],maxSurge:"50%",remark:"",eolinkerTest:!1},deployFormRules:{maxSurge:[{required:!0,message:"请选择每批升级数"}]}}},components:{ParentPomOptions:K["a"]},computed:{mavenOptions:function(){return this.$settings.mavenImages||[]},parentPoms:function(){return this.$settings.parentPoms||[]}},mounted:function(){this.loadGitModule(),this.loadPipeline()},methods:{loadGitModule:function(){var e=this;this.gitModuleLoading=!0,Object(q["n"])("",this.pipelineId).then((function(t){var a,i=Object(o["a"])(t.data);try{for(i.s();!(a=i.n()).done;){var n=a.value;n.tag="",n.branchOptions=[],n.tagOptions=[]}}catch(l){i.e(l)}finally{i.f()}e.buildForm.modules=t.data,e.loadGitTags()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.gitModuleLoading=!1}))},loadPipeline:function(){var e=this;this.pipelineLoading=!0,Object(l["e"])(this.pipelineId).then((function(t){e.pipeline=t.data})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.pipelineLoading=!1}))},loadGitTags:function(){var e=this;this.gitTagLoading=!0;var t,a=new Set,i=Object(o["a"])(this.buildForm.modules);try{for(i.s();!(t=i.n()).done;){var n=t.value;a.add(n.gitUrl)}}catch(l){i.e(l)}finally{i.f()}Object(J["c"])({gitUrls:Array.from(a)}).then((function(t){for(var a=0,i=Object.entries(t.data);a<i.length;a++){var n,r=Object(H["a"])(i[a],2),s=r[0],c=r[1],p=Object(o["a"])(e.buildForm.modules);try{for(p.s();!(n=p.n()).done;){var u=n.value;u.gitUrl===s&&(u.branchOptions=c.branchOptions,u.tagOptions=c.tagOptions)}}catch(l){p.e(l)}finally{p.f()}}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.gitTagLoading=!1}))},buildSubmit:function(){var e=this;this.$refs["buildFormRef"].validate((function(t){t&&e.$refs["deployFormRef"].validate((function(t){if(t){var a,i=Object(o["a"])(e.buildForm.modules);try{for(i.s();!(a=i.n()).done;){var n=a.value;if(""===n.tag)return void e.$message.error("请选择每个模块的版本号")}}catch(d){i.e(d)}finally{i.f()}e.submitLoading=!0;var l,s={buildImageParam:[],deployParam:[{pipelineId:e.pipeline.id,maxSurge:e.deployForm.maxSurge,remark:e.deployForm.remark,eolinkerTest:e.deployForm.eolinkerTest}]},c=Object(r["a"])(e.buildForm),p=Object(o["a"])(c.modules);try{for(p.s();!(l=p.n()).done;){var u=l.value;s.buildImageParam.push({app:e.pipeline.app,gitUrl:u.gitUrl,gitModule:u.module,gitTag:u.tag,mavenImage:u.mavenImage,unitTest:c.unitTest,forceCodeCompile:c.forceCodeCompile,dependencyCheck:c.dependencyCheck,parentPom:c.parentPom,remark:c.remark})}}catch(d){p.e(d)}finally{p.f()}Object(O["b"])(s).then((function(t){e.$message.success("操作成功"),e.$emit("successHandler"),1===t.data.length?e.jobDetailPage(t.data[0].jobId):e.jobHistoryPage("","")})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.submitLoading=!1}))}}))}))},jobDetailPage:function(e){var t=this;Object(O["g"])(e).then((function(a){var i=a.data,n="CD"===i.type?"cicd-app-deploy-detail":"cicd-image-build-detail",o=t.$router.resolve({name:n,query:{jobId:e}});window.open(o.href,"_blank")})).catch((function(e){t.$message.error(e.message)}))},jobHistoryPage:function(e,t){var a=this.$router.resolve({name:"cicd-image-build-history",query:{gitUrl:e,gitModule:t,author:""}});window.open(a.href,"_blank")}}},Q=G,W=(a("641a"),Object(v["a"])(Q,U,E,!1,null,null,null)),X=W.exports,Y=a("57c3"),Z={name:"app-deploy",data:function(){return{loading:!1,statusOptions:[{label:"所有",value:"_all_"},{label:"可用",value:"enabled"},{label:"禁用",value:"disabled"},{label:"已迁移",value:"migrated"}],statusFilter:"_all_",cloudCategories:[],cloudCategoryCurr:"_all_",currApp:"",tableDataAll:[],tableData:[],tableLoading:!1,tableColumnsShowMore:{baseImage:!1,createTime:!1,jvmOpts:!1},deployData:{dialogVisible:!1,dialogLoading:!1,form:{pipelineIds:[],maxSurge:"50%",remark:"",eolinkerTest:!1,deployModuleImages:[{gitUrl:"",gitModule:"",image:""}]},pipelines:[],imageOptions:[{gitUrl:"",gitModule:"",imageSelected:"",images:[{name:"",tag:"",parentPom:"",parentPomShortName:"",remark:""}]}]},removePipeDialogVisible:!1,removePipeDialogData:{},offlineAppDialogVisible:!1,addressVisible:!1,address:{cluster:"",namespace:"",app:""},dialogCloneVisible:!1,dialogCloneForm:{clusterOptions:[],namespaceOptions:[],app:"",sourcePipelineId:0,sourceCluster:"",sourceNamespace:"",targetNamespaces:[]},dialogPodTableVisible:!1,dialogPodTable:{cluster:"",namespace:"",app:""},dialogK8sDeploymentVisible:!1,dialogK8sDeployment:{cluster:"",namespace:"",app:""},bugfixBranchVisible:!1,bugfixBranch:{gitUrl:"",namespace:"",cluster:"",app:"",gitUrlOptions:[]},updateStatusDialogVisible:!1,updateStatusDialogData:{},buildAndDeployDialogVisible:!1,buildAndDeployPipelineId:0}},components:{MaintainAlert:Y["a"],BuildAndDeploy:X,ExportButton:N["a"],DeploymentDetail:z,AppAddress:T["a"],MenuTabs:A["a"],JobRunnerAlert:j["a"],AppSelector2:P["a"],PipelineApp:D["a"],PipelineDoc:C,PodSimpleTable:g,PipelineExpand:_["a"]},computed:{isProdEnv:function(){return window.location.host.indexOf("foneshare")>-1},cicdOneKey:function(){return!this.isProdEnv}},mounted:function(){},methods:{changeCurrApp:function(e){this.currApp=e,this.updateQueryParam(),this.loadTableData()},pipelineFilter:function(){var e=this.tableDataAll,t=[];if("_all_"===this.cloudCategoryCurr)t=e;else{var a,i=Object(o["a"])(e);try{for(i.s();!(a=i.n()).done;){var n=a.value;n.extraAttr.cloudCategory===this.cloudCategoryCurr&&t.push(n)}}catch(c){i.e(c)}finally{i.f()}}if(e=t,t=[],"_all_"===this.statusFilter)t=e;else{var l,r=Object(o["a"])(this.tableDataAll);try{for(r.s();!(l=r.n()).done;){var s=l.value;s.status===this.statusFilter&&t.push(s)}}catch(c){r.e(c)}finally{r.f()}}this.tableData=t},updateQueryParam:function(){var e=Object(r["a"])(this.$route.query);e["app"]=this.currApp,this.$router.push({query:e})},cloudCategoryStatistics:function(e){var t,a=[],i=Object(o["a"])(e);try{var n=function(){var e=t.value,i=a.filter((function(t){return t.name===e.extraAttr.cloudCategory}));if(i.length<1)return a.push({name:e.extraAttr.cloudCategory,desc:e.extraAttr.cloudCategoryDesc,pipelineCount:1}),"continue";i[0].pipelineCount+=1};for(i.s();!(t=i.n()).done;)n()}catch(l){i.e(l)}finally{i.f()}this.cloudCategories=a},loadTableData:function(){var e=this;this.tableLoading=!0,Object(l["c"])(this.currApp).then((function(t){t.data.map((function(e){e.pods=[],e.extraAttr.deployTag=null,e.extraAttr.deployModules=[],e.extraAttr.runningPodNum=null,e.extraAttr.autoScaleV2MinReplicas=null,e.extraAttr.autoScaleV2MaxReplicas=null,e.extraAttr.cronScaleMinReplicas=null,e.extraAttr.cronScaleMaxReplicas=null,e.extraAttr.deregisterPodSize=0,e.extraAttr.deployStatus=null,e.extraAttr.deployJobId=null})),e.cloudCategoryStatistics(t.data),e.tableDataAll=t.data,e.tableData=e.tableDataAll;var a,i=Object(o["a"])(e.tableData);try{for(i.s();!(a=i.n()).done;){var n=a.value;e.findDeployment(n.cluster,n.namespace,n.app),e.findScaleConfig(n.cluster,n.namespace,n.app),e.findDeregisterPods(n.cluster,n.namespace,n.app)}}catch(l){i.e(l)}finally{i.f()}e.loadCDJobs()})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1}))},loadCDJobs:function(){var e=this;if(this.currApp){var t={params:{},app:this.currApp,status:["WAIT","RUNNING"],type:"CD",page:1,limit:100};Object(O["k"])(t).then((function(t){var a,i=t.data.data,n=Object(o["a"])(i);try{for(n.s();!(a=n.n()).done;){var l,r=a.value,s=Object(o["a"])(e.tableData);try{for(s.s();!(l=s.n()).done;){var c=l.value;if(c.id===r.pipelineId){var p=null;"RUNNING"===r.status?p="发布中•••":"WAIT"===r.status&&(p="发布等待中•••"),c.extraAttr.deployStatus=p,c.extraAttr.deployJobId=r.id;break}}}catch(u){s.e(u)}finally{s.f()}}}catch(u){n.e(u)}finally{n.f()}})).catch((function(e){console.log("load job fail, "+e.message)}))}},urgentDeploy:function(){this.$alert('如果需要紧急发布，请申请临时操作权限。 点击 <a href="/#/auth/temp-path" target="_blank" style="color: #3a8ee6">链接</a> 打开临时授权申请页面',"紧急发布提示",{dangerouslyUseHTMLString:!0,confirmButtonText:"关闭"})},findDeployment:function(e,t,a){var i,n=null,l=Object(o["a"])(this.tableData);try{for(l.s();!(i=l.n()).done;){var r=i.value;if(r.cluster===e&&r.namespace===t&&r.app===a){n=r;break}}}catch(s){l.e(s)}finally{l.f()}null!==n&&Object(h["a"])(e,t,a).then((function(e){n.extraAttr.deployTag=e.data.deployTag,n.extraAttr.deployModules=e.data.deployModules,n.extraAttr.runningPodNum=e.data.replicas,n.extraAttr.lastDeployTime=e.data.deployTime,n.extraAttr.lastDeployUser=e.data.deployUser})).catch((function(e){n.extraAttr.deployTag="--",n.extraAttr.deployModules=[],n.extraAttr.runningPodNum=0,n.extraAttr.lastDeployTime=null,n.extraAttr.lastDeployUser=null,console.error(e.message)}))},findDeregisterPods:function(e,t,a){var i,n=null,l=Object(o["a"])(this.tableData);try{for(l.s();!(i=l.n()).done;){var r=i.value;if(r.cluster===e&&r.namespace===t&&r.app===a){n=r;break}}}catch(s){l.e(s)}finally{l.f()}null!==n&&Object(u["d"])(e,t,a,!1).then((function(e){n.extraAttr.deregisterPodSize=e.data.length})).catch((function(e){}))},findScaleConfig:function(e,t,a){var i,n=this,l=null,r=Object(o["a"])(this.tableData);try{for(r.s();!(i=r.n()).done;){var s=i.value;if(s.cluster===e&&s.namespace===t&&s.app===a){l=s;break}}}catch(c){r.e(c)}finally{r.f()}null!==l&&Object(B["h"])(e,t,a).then((function(e){e.data["autoScaleV2"]?(l.extraAttr.autoScaleV2MinReplicas=e.data["autoScaleV2"]["spec"]["minReplicaCount"],l.extraAttr.autoScaleV2MaxReplicas=e.data["autoScaleV2"]["spec"]["maxReplicaCount"]):(l.extraAttr.autoScaleV2MinReplicas="-",l.extraAttr.autoScaleV2MaxReplicas="-"),e.data["cronScale"]?(l.extraAttr.cronScaleMinReplicas=l.replicas,l.extraAttr.cronScaleMaxReplicas=e.data["cronScale"]["Replicas"]):(l.extraAttr.cronScaleMinReplicas="-",l.extraAttr.cronScaleMaxReplicas="-")})).catch((function(e){n.$message.error(e.message)}))},removePipe:function(){var e=this;this.$prompt("请输入应用名","下线确认提示",{confirmButtonText:"继续下线",confirmButtonClass:"el-button--danger",cancelButtonText:"取消"}).then((function(t){var a=t.value;a===e.removePipeDialogData.app?Object(l["k"])(e.removePipeDialogData.id).then((function(t){e.$message.success("删除成功"),e.loadTableData(e.$route.query.app)})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.removePipeDialogVisible=!1})):e.$message.info("应用名输入错误")})).catch((function(){console.log("取消下线确认操作")}))},removePipeDialog:function(e){this.removePipeDialogData=Object(r["a"])(e),this.removePipeDialogVisible=!0},offlineAppDialog:function(){this.offlineAppDialogVisible=!0},updateStatusDialog:function(e){this.updateStatusDialogData=Object(r["a"])(e),this.updateStatusDialogVisible=!0},updateStatus:function(){var e=this,t={};t.id=this.updateStatusDialogData.id,t.status=this.updateStatusDialogData.status,console.log(t),Object(l["o"])(t).then((function(t){e.$message.success("修改成功"),e.loadTableData(e.$route.query.app)})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.updateStatusDialogVisible=!1}))},createPage:function(){this.$router.push({name:"app-pipeline-edit",query:{app:this.currApp}})},tempAuthPage:function(){var e=this.$router.resolve({name:"auth-temp-auth",query:{showAddDialog:"true",app:this.currApp}}).href;window.open(e,"_blank")},batchDeploy:function(){var e=this.$refs.pipelineTable.store.states.selection;!e||e.length<1?this.$message.warning("请选择需要发布的流程"):this.showDeployDialog(e)},imageBuildPage:function(){var e=this.$router.resolve({name:"cicd-image-build",query:{app:this.currApp,openDialog:"true"}}).href;window.open(e,"_blank")},multiClusterPodExecPage:function(){var e=this.$refs.pipelineTable.store.states.selection;!e||e.length<1?this.$message.warning("请选择需要发布的流程"):this.$confirm("此操作将同时进入 ".concat(e.length," 个发布流程的第一个实例, 是否继续?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t,a=[],i=Object(o["a"])(e);try{for(i.s();!(t=i.n()).done;){var n=t.value;a.push({cluster:n.cluster,namespace:n.namespace,app:n.app})}}catch(s){i.e(s)}finally{i.f()}var l=JSON.stringify(a),r="/api/page/redirect?type=webShellWithFirstPod&webShellParams=".concat(l,"&_t")+Date.now();window.open(r)})).catch((function(){}))},editPage:function(e){var t={pipelineId:e.id};this.$router.push({name:"app-pipeline-edit",query:t})},syncConfigToOtherPage:function(e){var t={pipelineId:e.id,app:e.app};this.$router.push({name:"app-pipeline-sync-config",query:t})},showPodTable:function(e){this.dialogPodTable.cluster=e.cluster,this.dialogPodTable.namespace=e.namespace,this.dialogPodTable.app=e.app,this.dialogPodTableVisible=!0},scalePage:function(e,t){var a=null;if("autoscalev2"===t)a=this.$router.resolve({name:"pod-auto-scaler",query:{cluster:e.cluster,namespace:e.namespace,app:e.app}});else{if("cronscale"!==t)return void this.$message.error("未知的扩缩容类型："+t);a=this.$router.resolve({name:"app-scale-cron",query:{cluster:e.cluster,namespace:e.namespace,app:e.app}})}window.open(a.href,"_blank")},showK8sDeployment:function(e){this.dialogK8sDeployment.cluster=e.cluster,this.dialogK8sDeployment.namespace=e.namespace,this.dialogK8sDeployment.app=e.app,this.dialogK8sDeploymentVisible=!0},showCreateBugfixBranch:function(e){this.bugfixBranch.cluster=e.cluster,this.bugfixBranch.namespace=e.namespace,this.bugfixBranch.app=e.app,this.bugfixBranchVisible=!0,this.bugfixBranch.gitUrlOptions=[];var t,a=Object(o["a"])(e.appModules);try{for(a.s();!(t=a.n()).done;){var i=t.value;this.bugfixBranch.gitUrlOptions.includes(i.gitUrl)||this.bugfixBranch.gitUrlOptions.push(i.gitUrl)}}catch(n){a.e(n)}finally{a.f()}},createBugfixBranch:function(){var e=this;Object(q["f"])(this.bugfixBranch).then((function(t){e.$message.success("创建成功，分支名："+t.data+"。3秒后跳转到gitlab页面,请确保浏览器允许弹出新窗口"),setTimeout((function(){window.open(e.bugfixBranch.gitUrl.replace(".git","")+"/-/branches","_blank")}),3e3)})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.bugfixBranchVisible=!1}))},showDeployDialog:function(e){var t,a=this,i=e[0],n=Object(o["a"])(e);try{for(n.s();!(t=n.n()).done;){var l=t.value;if("enabled"!==l.status)return void this.$message.warning("有发布流程处于不可用状态");if(l.appModules.length!==i.appModules.length)return void this.$message.warning("发布流程之间的部署模块不一样，不能一起批量发布")}}catch(s){n.e(s)}finally{n.f()}this.loading=!0;var r=e.map((function(e){return e.id}));Object(O["i"])(r.join(",")).then((function(t){a.loading=!1,a.deployData.pipelines=e,a.deployData.imageOptions=t.data;var i="50%";a.$settings.maxSurgeForceFull&&(i="100%");var n=!1;a.$settings.eolinkerTestDefault&&(n=!0),a.deployData.form={pipelineIds:r,maxSurge:i,remark:"",eolinkerTest:n,deployModuleImages:[]},a.deployData.dialogVisible=!0})).catch((function(e){a.loading=!1,a.$message.error(e.message)}))},imageTagChange:function(e){var t,a=Object(o["a"])(this.deployData.imageOptions);try{for(a.s();!(t=a.n()).done;){var i,n=t.value,l=Object(o["a"])(n.images);try{for(l.s();!(i=l.n()).done;){var r=i.value;if(r.name===e&&r.remark){this.deployData.form.remark=r.remark;break}}}catch(s){l.e(s)}finally{l.f()}}}catch(s){a.e(s)}finally{a.f()}},manageDropdownCommand:function(e){console.log("manage dropdown command: "+e);var t=e.split("##");if(2===t.length){var a=t[0],i=parseInt(t[1]),n=this.tableData.filter((function(e){return e.id===i}))[0];"edit"===a?this.editPage(n):"showAddr"===a?this.showAddress(n):"remove"===a?this.removePipeDialog(n):"clone"===a?this.showCloneDialog(n):"deployHistory"===a?this.jobHistoryPage(n.app,n.namespace):"address"===a?this.showAddress(n):"updateStatus"===a?this.updateStatusDialog(n):"syncConfigToOther"===a?this.syncConfigToOtherPage(n):"appLog"===a?this.clickhouseLogPage("app_log",n.cluster,n.namespace,n.app):"k8sDeployment"===a?this.showK8sDeployment(n):"bugfixBranch"===a?this.showCreateBugfixBranch(n):this.$message.error("未知操作："+e)}},deploySubmit:function(){var e=this;this.$refs["dialogDeployForm"].validate((function(t){if(!t)return!1;var a,i=Object(o["a"])(e.deployData.imageOptions);try{for(i.s();!(a=i.n()).done;){var n=a.value;if(!n.imageSelected)return e.$message.error("请选择镜像版本"),!1}}catch(r){i.e(r)}finally{i.f()}var l=e.$settings.envConfirmText;l?e.$prompt('请把红色内容输入到文本框: <b style="color:orangered">'.concat(l,"</b> "),"环境确认",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0}).then((function(t){var a=t.value;a===l?e.processDeploy():e.$message.warning("环境确认失败，请重新输入")})):e.processDeploy()}))},processDeploy:function(){var e=this;this.deployData.dialogLoading=!0;var t,a=[],i=Object(o["a"])(this.deployData.imageOptions);try{for(i.s();!(t=i.n()).done;){var n=t.value;a.push({gitUrl:n.gitUrl,gitModule:n.gitModule,image:n.imageSelected})}}catch(p){i.e(p)}finally{i.f()}var l,r={items:[]},s=Object(o["a"])(this.deployData.form.pipelineIds);try{for(s.s();!(l=s.n()).done;){var c=l.value;r.items.push({pipelineId:c,maxSurge:this.deployData.form.maxSurge,remark:this.deployData.form.remark,eolinkerTest:this.deployData.form.eolinkerTest,deployModuleImages:a})}}catch(p){s.e(p)}finally{s.f()}Object(O["e"])(r).then((function(t){1===t.data.length?e.jobPage(t.data[0].jobId):e.jobHistoryPage(e.currApp,""),e.deployData.dialogVisible=!1})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.$refs["jobRunnerAlert"].loadJobs(),e.loadCDJobs(),e.deployData.dialogLoading=!1}))},jobPage:function(e){var t=this.$router.resolve({name:"cicd-app-deploy-detail",query:{jobId:e}});window.open(t.href,"_blank")},jobHistoryPage:function(e,t){var a=this.$router.resolve({name:"cicd-app-deploy-history",query:{app:e,namespace:t}});window.open(a.href,"_blank")},showCloneDialog:function(e){var t=this;this.dialogCloneForm.app=e.app,this.dialogCloneForm.sourcePipelineId=e.id,this.dialogCloneForm.sourceCluster=e.cluster,this.dialogCloneForm.sourceNamespace=e.namespace;var a,i=[],n=Object(o["a"])(this.$settings.clusters);try{var l=function(){var e,n=a.value,l=Object(o["a"])(n.namespaces);try{var r=function(){var a=e.value;if(t.tableData.filter((function(e){return e.cluster===n.name&&e.namespace===a})).length>0)return console.log("pipeline has exist, remove namespace option: ".concat(n.name,"/").concat(a)),"continue";i.push("".concat(n.name,"/").concat(a))};for(l.s();!(e=l.n()).done;)r()}catch(s){l.e(s)}finally{l.f()}};for(n.s();!(a=n.n()).done;)l()}catch(r){n.e(r)}finally{n.f()}this.dialogCloneForm.namespaceOptions=i,this.dialogCloneVisible=!0},buildAndDeployDialog:function(e){this.buildAndDeployPipelineId=e.id,this.buildAndDeployDialogVisible=!0},clonePipeline:function(){var e=this,t=this.dialogCloneForm;if(t.sourcePipelineId)if(t.targetNamespaces){var a,i=Object(o["a"])(t.targetNamespaces);try{for(i.s();!(a=i.n()).done;){var n=a.value,l=n.split("/");if(2!==l.length)return void this.$message.error("目标环境格式错误");var r=this.$router.resolve({name:"app-pipeline-edit",query:{operate:"clone",pipelineId:t.sourcePipelineId,targetCluster:l[0],targetNamespace:l[1]}});window.open(r.href,"_blank"),setTimeout((function(){e.dialogCloneVisible=!1}),500)}}catch(s){i.e(s)}finally{i.f()}}else this.$message.error("请选择目标环境 ");else this.$message.error("源环境信息缺失")},clickhouseLogPage:function(e,t,a,i){var n="/api/page/redirect?type=clickhouse&logName=".concat(e,"&cluster=").concat(t,"&namespace=").concat(a,"&app=").concat(i,"&_t")+Date.now();window.open(n)},showAddress:function(e){this.address={cluster:e.cluster,namespace:e.namespace,app:e.app},this.addressVisible=!0},convertStatus:function(e){switch(e){case"enabled":return"可用";case"disabled":return"禁用";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}},cellStyle:function(e){var t=e.row,a=e.column;e.rowIndex,e.columnIndex;if("运行数"===a.label&&t.extraAttr&&t.extraAttr.runningPodNum<1)return"background-color:#ffb980"}}},ee=Z,te=(a("f604"),Object(v["a"])(ee,i,n,!1,null,null,null));t["default"]=te.exports},a527:function(e,t,a){"use strict";a.d(t,"h",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"i",(function(){return l})),a.d(t,"e",(function(){return r})),a.d(t,"g",(function(){return s})),a.d(t,"c",(function(){return c})),a.d(t,"k",(function(){return p})),a.d(t,"l",(function(){return u})),a.d(t,"m",(function(){return d})),a.d(t,"o",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"r",(function(){return v})),a.d(t,"b",(function(){return b})),a.d(t,"a",(function(){return g})),a.d(t,"p",(function(){return h})),a.d(t,"q",(function(){return _})),a.d(t,"n",(function(){return y})),a.d(t,"j",(function(){return x}));a("96cf"),a("3b8d");var i=a("b775");function n(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:e,namespace:t,app:a}})}function o(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}function l(e,t){return Object(i["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:e,namespace:t}})}function r(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:e,namespace:t,pod:a}})}function s(e,t,a,n,o,l){return Object(i["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:e,namespace:t,pod:a,container:n,tailLines:o,previous:l}})}function c(e,t,a,i,n){var o="/api/v1/k8s/pod/stdout/download?cluster=".concat(e,"&namespace=").concat(t,"&pod=").concat(a,"&container=").concat(i,"&tailLines=").concat(n,'&_time="')+(new Date).getTime();window.open(o)}function p(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:e,namespace:t,pod:a}})}function u(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:e,namespace:t,pod:a}})}function d(e){return Object(i["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:e}})}function m(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:e,namespace:t,pod:a}})}function f(e){return Object(i["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:e})}function v(e){return Object(i["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:e})}function b(e,t){window.open("/api/v1/k8s/pod/file/download?fileId="+e+"&fileName="+t+"&_time="+(new Date).getTime())}function g(e){return Object(i["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:e})}function h(e){window.open("/api/v1/k8s/pod/file/preview?fileId="+e)}function _(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:e,namespace:t,pod:a}})}function y(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:e,namespace:t,pod:a}})}function x(e,t,a){return Object(i["a"])({url:"/v1/k8s/pod/open-pyroscope",method:"post",data:{cluster:e,namespace:t,pod:a}})}},aae3:function(e,t,a){var i=a("d3f4"),n=a("2d95"),o=a("2b4c")("match");e.exports=function(e){var t;return i(e)&&(void 0!==(t=e[o])?!!t:"RegExp"==n(e))}},b674:function(e,t,a){},bb0b:function(e,t,a){"use strict";a.d(t,"i",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"k",(function(){return r})),a.d(t,"j",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"c",(function(){return p})),a.d(t,"e",(function(){return u})),a.d(t,"g",(function(){return d})),a.d(t,"f",(function(){return m})),a.d(t,"h",(function(){return f}));var i=a("b775");function n(e){return Object(i["a"])({url:"/v1/k8s/scale/cron",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/v1/k8s/scale/cron",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/v1/k8s/scale/cron",method:"delete",params:{id:e}})}function r(e){return Object(i["a"])({url:"/v1/k8s/scale/log",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler",method:"post",data:e})}function p(e){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler/create-for-core-app",method:"post",params:e})}function u(e){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler",method:"delete",data:e})}function d(e){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler/migrate?cluster="+e,method:"post"})}function m(){return Object(i["a"])({url:"/v1/k8s/scale/podautoscaler/all-cluster-autoscaler-v2",method:"get"})}function f(e,t,a){return Object(i["a"])({url:"/v1/k8s/scale/all-by-app",method:"get",params:{cluster:e,namespace:t,app:a}})}},bfce:function(e,t,a){"use strict";a("699c")},cae6:function(e,t,a){},cf89:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.stdout.loading,expression:"stdout.loading"}],staticClass:"pod-stdout"},[a("div",{staticStyle:{position:"relative"}},[a("div",{staticStyle:{"font-weight":"bold",float:"left","line-height":"40px"}},[e._v(e._s(this.pod)+" /\n      "),a("el-select",{staticStyle:{width:"240px"},attrs:{size:"mini",placeholder:"请选择容器"},on:{change:e.loadStdoutLog},model:{value:e.container,callback:function(t){e.container=e._n(t)},expression:"container"}},e._l(e.containers,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),e._v(" "),a("div",{staticStyle:{float:"right","margin-right":"20px"}},[a("span",[a("el-checkbox",{on:{change:e.loadStdoutLog},model:{value:e.stdout.previous,callback:function(t){e.$set(e.stdout,"previous",t)},expression:"stdout.previous"}},[e._v("重启前日志")])],1),e._v(" "),a("span",{staticStyle:{"margin-left":"20px"}},[e._v("\n            行数:\n            "),a("el-select",{staticStyle:{width:"120px"},on:{change:e.loadStdoutLog},model:{value:e.stdout.tailLines,callback:function(t){e.$set(e.stdout,"tailLines",e._n(t))},expression:"stdout.tailLines"}},[a("el-option",{attrs:{label:"2000",value:"2000"}}),e._v(" "),a("el-option",{attrs:{label:"5000",value:"5000"}}),e._v(" "),a("el-option",{attrs:{label:"10000",value:"10000"}}),e._v(" "),a("el-option",{attrs:{label:"50000",value:"50000"}})],1)],1),e._v(" "),a("span",{staticStyle:{display:"none"}},[e._v("\n        自动刷新("+e._s(e.stdout.reloadPeriod)+"秒):\n      "),a("el-switch",{on:{change:e.autoReloadSwitch},model:{value:e.stdout.autoReload,callback:function(t){e.$set(e.stdout,"autoReload",t)},expression:"stdout.autoReload"}})],1),e._v(" "),a("el-button",{staticClass:"el-icon-refresh",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(t){return e.loadStdoutLog()}}},[e._v("刷新\n      ")]),e._v(" "),a("el-button",{staticClass:"el-icon-download",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(t){return e.podStdoutLogDownload()}}},[e._v("下载\n      ")])],1),e._v(" "),a("div",{staticStyle:{clear:"both"}})]),e._v(" "),a("div",{staticStyle:{"text-align":"right","margin-right":"5px"}},[e._v("加载时间: "+e._s(e.stdout.lastReloadTime))]),e._v(" "),a("pre",{staticClass:"stdout-log-content",attrs:{id:"stdout-log-content"}},[e._v(e._s(e.stdout.content))])])},n=[],o=a("a527"),l={name:"PodStdout",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!1},containers:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{container:this.containers[0],stdout:{visible:!1,loading:!1,autoReload:!1,previous:!1,tailLines:2e3,reloadPeriod:10,reloadTimer:null,content:"",lastReloadTime:"--"}}},watch:{pod:function(e,t){this.container=this.containers[0],this.loadStdoutLog()}},computed:{},mounted:function(){this.loadStdoutLog()},beforeDestroy:function(){this.stopReloadTimer()},methods:{showStdoutLogDialog:function(){this.stdout.visible=!0,this.loadStdoutLog(),this.stdout.autoReload&&this.startReloadTimer()},loadStdoutLog:function(){var e=this;this.pod&&(console.log("load pod ".concat(this.pod," stdout log")),this.stdout.loading=!0,Object(o["g"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines,this.stdout.previous).then((function(t){e.stdout.content=t.data;var a=e;setTimeout((function(){a.scrollStdoutLogView()}),200),setTimeout((function(){a.scrollStdoutLogView()}),500),setTimeout((function(){a.scrollStdoutLogView()}),700),e.stdout.lastReloadTime=(new Date).toLocaleTimeString()})).catch((function(t){e.$message.error(t.message),e.stopReloadTimer()})).finally((function(){e.stdout.loading=!1})))},podStdoutLogDownload:function(){Object(o["c"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines)},stopReload:function(){this.stdout.autoReload=!1,this.stopReloadTimer()},scrollStdoutLogView:function(){var e=document.getElementById("stdout-log-content");e.scrollTop=e.scrollHeight},startReloadTimer:function(){this.stdout.reloadTimer&&this.stopReloadTimer();var e=this;this.stdout.reloadTimer=setInterval((function(){e.loadStdoutLog()}),1e3*e.stdout.reloadPeriod),console.log("started pod stdout log reload timer :"+this.stdout.reloadTimer)},stopReloadTimer:function(){clearInterval(this.stdout.reloadTimer),console.log("stopped pod stdout log reload timer :"+this.stdout.reloadTimer)},autoReloadSwitch:function(e){this.stdout.autoReload=e,e?this.startReloadTimer():this.stopReloadTimer()}}},r=l,s=(a("fbec"),a("2877")),c=Object(s["a"])(r,i,n,!1,null,"6ff71a9f",null);t["a"]=c.exports},d152:function(e,t,a){"use strict";a("579f")},d2c8:function(e,t,a){var i=a("aae3"),n=a("be13");e.exports=function(e,t,a){if(i(t))throw TypeError("String#"+a+" doesn't accept regex!");return String(n(e))}},de9b:function(e,t,a){"use strict";a("b674")},e18c:function(e,t,a){},f604:function(e,t,a){"use strict";a("cae6")},fbec:function(e,t,a){"use strict";a("e18c")},ffc1:function(e,t,a){var i=a("5ca1"),n=a("504c")(!0);i(i.S,"Object",{entries:function(e){return n(e)}})}}]);