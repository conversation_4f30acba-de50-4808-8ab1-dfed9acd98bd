<template>
  <div class="app-container app-deploy-container" v-loading="loading" element-loading-text="数据加载中" style="margin: 0;padding: 0">
    <menu-tabs tab-name="app-deploy"></menu-tabs>
    <maintain-alert maintain-type="cd"></maintain-alert>

    <app-selector2 @change="changeCurrApp" :show-detail="true" :update-history="true"></app-selector2>

    <div v-show="this.currApp" style="min-height: 180px;margin: 10px">
      <job-runner-alert :app="this.currApp" ref="jobRunnerAlert" job-type="CD"></job-runner-alert>
      <div style="padding-top: 10px;">
        <div style="float:left;">
          <el-button type="primary" size="small" icon="el-icon-position" @click="batchDeploy">批量发布</el-button>
          <el-button type="text" icon="el-icon-circle-plus-outline" @click="createPage">新建流程</el-button>
          <el-button type="text" icon="el-icon-price-tag" style="margin-left: 10px;" @click="imageBuildPage">构建镜像</el-button>
          <el-button type="text" icon="el-icon-document" @click="offlineAppDialog">服务下线说明</el-button>
          <el-button type="text" icon="el-icon-price-console" style="margin-left: 10px;color:#bbb;" @click="multiClusterPodExecPage">
            <svg-icon icon-class="console"/>
            批量进入第一个容器
            <el-tooltip effect="dark" placement="top" content="同时进入所勾选发布流程下的第一个Pod容器里">
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </el-button>
          <el-button type="text" icon="el-icon-position" style="margin-left: 30px;color: #E6A23C" @click="urgentDeploy">紧急发布</el-button>


        </div>
        <div style="float: right">
          <export-button :table-ref="this.$refs.pipelineTable"></export-button>
          <el-dropdown trigger="click" :hide-on-click="false">
            <span class="el-dropdown-link">
              显示更多<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-checkbox v-model="tableColumnsShowMore.baseImage">基础镜像</el-checkbox>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-checkbox v-model="tableColumnsShowMore.createTime">创建时间</el-checkbox>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-checkbox v-model="tableColumnsShowMore.jvmOpts">JVM参数</el-checkbox>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <span style="font-size: 12px;color: #E6A23C;font-weight: bold">状态筛选：</span>
          <el-select v-model="statusFilter" size="small" style="width: 120px;margin-right: 20px" @change="pipelineFilter">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-radio-group size="mini" @input="pipelineFilter" fill="#909399" v-model="cloudCategoryCurr">
            <el-radio-button label="_all_">所有
              <span style="color:#fff;border-radius: 10px;display: inline-block;line-height: 1.4em;width: 20px;">
               &nbsp;
              </span>
            </el-radio-button>
            <el-radio-button v-for="item in cloudCategories" :label="item.name">{{ item.desc }}
              <span style="color:#fff;background-color: #409eff;border-radius: 10px;display: inline-block;line-height: 1.4em;width: 20px;">
                {{ item.pipelineCount }}
              </span>
            </el-radio-button>
          </el-radio-group>
        </div>
        <div style="clear: both"></div>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        :highlight-selection-row="true"
        element-loading-text="数据加载中..."
        ref="pipelineTable"
        border
        fit
        :cell-style="cellStyle">
        <el-table-column
          type="selection"
          align="center"
          width="40">
        </el-table-column>
        <el-table-column type="expand" label="详情" width="50">
          <template slot-scope="scope">
            <pipeline-expand :pipeline="scope.row"></pipeline-expand>
          </template>
        </el-table-column>
        <el-table-column type="index" width="40"></el-table-column>
        <el-table-column label="状态" width="70" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 'enabled'" type="success" size="mini" style="font-weight: bold;">
              {{ convertStatus(scope.row.status) }}
            </el-tag>
            <el-tag v-else type="warning" size="small" style="font-weight: bold;">
              {{ convertStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          :width="cicdOneKey? '290px' : '200px'">
          <template slot-scope="scope">
            <div style="font-size: 12px;">
              <router-link :to="{name:'pod-index',query:{'cluster':scope.row.cluster,'namespace':scope.row.namespace,'app':scope.row.app}}" target="_blank">
                <i class="el-icon-menu" style="color:#409EFF;font-weight: 500;">实例</i>
              </router-link>
              <router-link :to="{name: 'app-pipeline-edit', query: {pipelineId: scope.row.id}}" target="_blank" style="">
                <i class="el-icon-edit" style="color:#409EFF;font-weight: 500;">编辑</i>
              </router-link>
              <el-dropdown @command="manageDropdownCommand">
                <span class="el-dropdown-link" style="font-size: 12px;margin-left: 5px;">
                  更多<i class="el-icon-arrow-down "></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="el-icon-link" :command="'showAddr##' + scope.row.id">查看访问地址</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-position" :command="'deployHistory##' + scope.row.id">发布历史</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-tickets" :command="'bugfixBranch##' + scope.row.id">创建Bugfix分支</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-tickets" :command="'appLog##' + scope.row.id">Logback日志</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-copy-document" :command="'clone##' + scope.row.id">克隆</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-document-copy" :command="'syncConfigToOther##' + scope.row.id">配置同步到其他流程</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-document-copy" :command="'k8sDeployment##' + scope.row.id">k8s部署配置</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-setting" :command="'updateStatus##' + scope.row.id">修改状态</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-scissors" :command="'remove##' + scope.row.id" style="color: orange">下线</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-tooltip effect="dark" content="同时执行【镜像构建】和【服务发布】" placement="top">
                <el-button type="success"
                           size="mini"
                           v-if="cicdOneKey"
                           style="padding: 5px 7px;"
                           :disabled="scope.row.status !== 'enabled'"
                           @click="buildAndDeployDialog(scope.row)">
                  构建+发布
                </el-button>
              </el-tooltip>
              <el-button type="primary"
                         size="mini"
                         class="el-icon-position"
                         style="padding: 5px 7px;"
                         :disabled="scope.row.status !== 'enabled'"
                         @click="showDeployDialog([scope.row])">发布
              </el-button>
            </div>
            <div>
              <el-alert
                v-for="item in scope.row.extraAttr.clusterLabels "
                :key="item"
                :title="item"
                type="info"
                :show-icon="true"
                class="env-label-alert"
                :closable="false">
              </el-alert>
              <el-alert
                v-if="scope.row.namespace === 'fstest-metadata'"
                title="元数据专用环境,咨询李磊"
                type="info"
                :show-icon="true"
                class="env-label-alert"
                :closable="false">
              </el-alert>
              <el-alert
                v-if="scope.row.extraAttr.deregisterPodSize > 0"
                :title="`摘除了${scope.row.extraAttr.deregisterPodSize}个pod，不受发版影响`"
                type="info"
                :show-icon="true"
                class="env-label-alert"
                :closable="false">
              </el-alert>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="运行环境" prop="namespace" width="160">
          <template slot-scope="slot" slot="header">
            运行环境
            <el-tooltip effect="dark" placement="top">
              <template slot="content">
                <div>第一行为运行环境名，第二行为k8s集群名</div>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div style="font-weight: bold;">
              {{ scope.row.namespace }}
            </div>
            <div style="font-weight: normal;color: #666;font-size: 10px;margin-top: -8px">
              {{ scope.row.cluster }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="" width="180">
          <template slot-scope="slot" slot="header">
            云环境
            <el-tooltip effect="dark" placement="top">
              <template slot="content">
                <div>第一行为云环境名，第二行为云类型</div>
                <hr/>
                <div>云类型分为：</div>
                <p>纷享云：部署在纷享机房</p>
                <p>公有云：部署在纷享购买的公有云平台（阿里云、华为云、腾讯云、AWS）</p>
                <p>专属云：部署在客户购买的三大公有云平台（阿里云、华为云、腾讯云）</p>
                <p>混合云：部署在客户自建机房</p>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div style="font-weight: bold;">
              <div>{{ scope.row.extraAttr.clusterSummary }}</div>
              <div>
                <div v-if="scope.row.extraAttr && scope.row.extraAttr.cloudCategoryDesc" style="font-weight: normal;color: #666;font-size: 10px;margin-top: -8px">
                  {{ scope.row.extraAttr.cloudCategoryDesc }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="基础镜像" v-if="tableColumnsShowMore.baseImage" min-width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.baseImage.substring(scope.row.baseImage.lastIndexOf("/") + 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" v-if="tableColumnsShowMore.createTime" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.createdTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="JVM参数" v-if="tableColumnsShowMore.jvmOpts" min-width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.jvmOpts }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部署模块" width="80">
          <template slot-scope="scope">
            {{ scope.row.appModules.length }}
            <el-popover
              placement="right"
              width="680"
              align="center"
              trigger="click">
              <el-table :data="scope.row.appModules" style="max-height: 460px;overflow-y: auto;">
                <el-table-column type="index" width="40"></el-table-column>
                <el-table-column prop="gitUrl" label="Git地址"></el-table-column>
                <el-table-column prop="module" label="子模块"></el-table-column>
                <el-table-column prop="contextPath" label="ContextPath"></el-table-column>
              </el-table>
              <el-button slot="reference" type="text" style="margin-left: 5px;font-size: 12px;">查看</el-button>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="运行版本" prop="extraAttr.deployTag" show-overflow-tooltip min-width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.extraAttr.deployModules" style="display:inline-block;margin-right: 5px;">
              <el-popover
                placement="left"
                width="760"
                trigger="click">
                <el-table :data="scope.row.extraAttr.deployModules" style="max-height: 480px;overflow-y: auto;">
                  <el-table-column type="index" width="40"></el-table-column>
                  <el-table-column prop="gitUrl" label="Git地址"></el-table-column>
                  <el-table-column prop="module" label="子模块"></el-table-column>
                  <el-table-column prop="tag" label="代码分支/标签"></el-table-column>
                  <el-table-column prop="commitIdShort" label="提交ID"></el-table-column>
                  <el-table-column prop="contextPath" label="ContextPath"></el-table-column>
                </el-table>
                <el-button slot="reference" type="text" style="margin: 0;padding:0;font-size: 12px;">详情</el-button>
              </el-popover>
            </div>
            <span v-if="scope.row.extraAttr.deployTag === null"><i class="el-icon-loading"></i></span>
            <span v-else>{{ scope.row.extraAttr.deployTag }}</span>
            <div style="width: 100%;background-color: rgb(236 140 61);font-size: 12px;color: white;font-weight: bold;line-height: 2em;height: 2em;" v-if="scope.row.extraAttr.deployStatus">
              <i class="el-icon-loading"></i>
              {{ scope.row.extraAttr.deployStatus }}
              <router-link :to="{name:'cicd-app-deploy-detail',query:{'jobId':scope.row.extraAttr.deployJobId}}" target="_blank" style="text-decoration: underline;margin-left: 3px;">
                [进入详情页]
              </router-link>
            </div>

          </template>
        </el-table-column>
        <el-table-column label="最近发布" width="110" show-overflow-tooltip>
          <template slot-scope="scope">
            <div style="font-size: 10px;line-height: 1.8em;overflow: hidden">
              <div v-if="scope.row.extraAttr.lastDeployTime">{{ scope.row.extraAttr.lastDeployTime }}</div>
              <div v-if="scope.row.extraAttr.lastDeployUser">发布人：{{ scope.row.extraAttr.lastDeployUser }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="" width="110">
          <template slot-scope="slot" slot="header">
            扩缩容
            <el-tooltip effect="dark" placement="top">
              <template slot="content">
                <p>扩缩容类型：最低副本数 → 最大副本数</p>
                <p>比如：</p>
                <hr/>
                <p>自动: 4 → 12 （配置了自动扩缩容，且可扩容的最大副本数为12）</p>
                <p>定时: 4 → 8 （配置了定时扩缩容，且可扩容的最大副本数为8）</p>
              </template>
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div style="font-size: 10px;line-height: 1.8em;overflow: hidden">
              <div v-if="scope.row.extraAttr.autoScaleV2MaxReplicas === null"><i class="el-icon-loading"></i></div>
              <div v-else>
                <div v-if="scope.row.extraAttr.autoScaleV2MaxReplicas !== '-' " style="position: relative;">
                  <b>自动:</b>
                  {{ scope.row.extraAttr.autoScaleV2MinReplicas }} → {{ scope.row.extraAttr.autoScaleV2MaxReplicas }}
                  <el-button type="text" style="font-size: 10px;padding: 0;margin:0" @click="scalePage(scope.row,'autoscalev2')">查看</el-button>
                </div>
                <div v-if="scope.row.extraAttr.cronScaleMaxReplicas !== '-' ">
                  <b>定时:</b>
                  {{ scope.row.extraAttr.cronScaleMinReplicas }} → {{ scope.row.extraAttr.cronScaleMaxReplicas }}
                  <el-button type="text" style="font-size: 10px;padding: 0;margin:0" @click="scalePage(scope.row,'cronscale')">查看</el-button>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="实例（Pod)" align="center">
          <el-table-column width="80" align="center" property="replicas">
            <template slot-scope="slot" slot="header" label="配置数">
              配置数
              <el-tooltip effect="dark" content="发布流程里配置的实例数" placement="top">
                <svg-icon icon-class="question" class-name="question-icon-size"/>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column width="80" align="center" label="运行数">
            <template slot-scope="slot" slot="header">
              运行数
              <el-tooltip effect="dark" content="当前运行的实例数" placement="top">
                <svg-icon icon-class="question" class-name="question-icon-size"/>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span v-if="scope.row.extraAttr.runningPodNum === null"><i class="el-icon-loading"></i></span>
              <span v-else>{{ scope.row.extraAttr.runningPodNum }}</span>
              <el-button v-if="scope.row.extraAttr.runningPodNum > 0" slot="reference" type="text"
                         style="margin-left: 5px;font-size:12px;" @click="showPodTable(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="CPU"
                         width="90"
                         prop="resources.limitCPU">
          <template slot-scope="slot" slot="header">
            CPU
            <el-tooltip effect="dark" content="最低要求值 - 最大可用值" placement="top">
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </template>
          <template slot-scope="scope" v-if="scope.row.resources">
            {{ scope.row.resources.requestCPU.toFixed(1) }} - {{ scope.row.resources.limitCPU.toFixed(1) }}
          </template>
        </el-table-column>
        <el-table-column width="110px"
                         prop="resources.limitMemory">
          <template slot-scope="slot" slot="header">
            内存 (MB)
            <el-tooltip effect="dark" content="最低要求值 - 最大可用值" placement="top">
              <svg-icon icon-class="question" class-name="question-icon-size"/>
            </el-tooltip>
          </template>
          <template slot-scope="scope" v-if="scope.row.resources">
            {{ scope.row.resources.requestMemory }} - {{ scope.row.resources.limitMemory }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="margin: 10px;">
      <pipeline-doc></pipeline-doc>
    </div>

    <el-dialog :visible.sync="deployData.dialogVisible" :close-on-click-modal="false" top="5vh" width="960px"
               class="deploy-dialog">
      <template slot="title">
        应用发布 <small style="padding-left: 20px;color: #666;">（默认会选择当前运行的版本号）</small>
      </template>
      <el-form :model="deployData.form" ref="dialogDeployForm" label-width="100px">
        <div v-for="(item,index) in deployData.imageOptions">
          <el-form-item label="" class="module-git-url">
            <span style="color: #b4532a	;font-weight: bold;">
              Git地址---模块： {{ item.gitUrl }} --- {{ item.gitModule ? item.gitModule : '[空]' }}
            </span>
          </el-form-item>
          <el-form-item label="镜像版本">
            <div style="display: inline-block;width: 800px;">
              <el-select style="width: 100%" filterable v-model="item.imageSelected"
                         @change="imageTagChange">
                <el-option value="">
                  <div style="font-weight: bold;color: #888;font-size: 12px;">
                    <span style="display: inline-block;width: 50%">版本</span>
                    <span style="display: inline-block;width: 10%">父POM</span>
                    <span style="display: inline-block;width: 20%">创建时间</span>
                    <span style="display: inline-block;width: 20%">备注</span>
                  </div>
                </el-option>
                <el-option
                  v-for="item in item.images"
                  :key="item.name"
                  :label="item.tag + '          (父pom: ' + item.parentPomShortName + ')'"
                  :value="item.name">
                  <div>
                    <span style="display: inline-block;width: 50%;"><b>{{ item.tag }}</b></span>
                    <span style="display: inline-block;width: 10%;">{{ item.parentPomShortName }}</span>
                    <span style="display: inline-block;width: 20%;">{{ item.createTime }}</span>
                    <span style="display: inline-block;width: 20%;">{{ item.remark ? item.remark.length > 20 ? item.remark.substring(0, 20) + '...' : item.remark : '' }}</span>
                  </div>
                </el-option>
                <div v-if="!item.images" style="color: #888;text-align: center;font-size: 13px;margin: 10px">
                  无可用的镜像， 去
                  <el-button type="text" @click="imageBuildPage" style="font-size: 13px;">构建镜像</el-button>
                </div>
              </el-select>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="每批升级数">
          <div style="display: inline-block;width: 800px;">
            <el-select v-model="deployData.form.maxSurge" style="width: 100%">
              <el-option-group label="按百分比">
                <el-option label="100%" value="100%"></el-option>
                <el-option label="50%" value="50%"></el-option>
                <el-option label="25%" value="25%"></el-option>
              </el-option-group>
              <el-option-group label="按个数">
                <el-option label="3" value="3"></el-option>
                <el-option label="2" value="2"></el-option>
                <el-option label="1" value="1"></el-option>
              </el-option-group>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="发布描述">
          <div style="display: inline-block;width: 800px;">
            <el-input v-model="deployData.form.remark" type="textarea" :rows="2" :maxlength="256" :max="256"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="发布环境" style="margin-bottom: 0">
          <el-tag style="font-weight: bold;margin-right: 8px;font-size: 14px;" v-for="pipe in this.deployData.pipelines" effect="plain">
            {{ pipe.namespace }} ({{ pipe.cluster }})
          </el-tag>
          <div style="color: #888;line-height: 1.3em;" v-if="this.deployData.pipelines.length > 1">
            提示： 批量发布时，会首先发布第一个环境，等发布成功后再同时并行发布所有剩余环境
          </div>
          <div style="line-height: 1.3em;color:#f3794d" v-if="this.deployData.pipelines.filter(item => item.namespace === 'forceecrm-public-prod').length > 0">
            提示： 复制云环境的发布，只能选择复制云专用父POM所构建出来的镜像
          </div>
        </el-form-item>
        <el-form-item>
          <el-checkbox label="执行Eolinker接口测试" v-model="deployData.form.eolinkerTest"
                       style="margin: 0 0 0 0"></el-checkbox>
          <el-tooltip class="item" effect="light" placement="top">
            <template slot="content">
              是否执行Eolinker接口测试？如果发布流程没有配置Eolinker接口测试，则该选项不会产生任何作用<br/>
              大版本发布期间，建议关闭Eolinker接口测试可以提升发布效率，避免因接口测试导致发布失败。
            </template>
            <i class="el-icon-info"></i>
          </el-tooltip>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="imageBuildPage" style="font-size: 13px;margin-right: 20px;">去构建镜像</el-button>
        <el-button @click="deployData.dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="deploySubmit" v-loading="deployData.dialogLoading">发 布</el-button>
      </div>
    </el-dialog>

    <el-dialog title="发布流程复制" :visible.sync="dialogCloneVisible" width="40%" :close-on-click-modal="false">
      <el-form :model="dialogCloneForm" ref="dialogCloneForm" label-width="80px">
        <el-form-item label="应用名" prop="app">
          <el-input v-model.trim="dialogCloneForm.app" :disabled="true"></el-input>
        </el-form-item>

        <el-form-item label="源环境">
          <el-row>
            <el-col :span="12" style="padding-right: 20px;">
              <el-form-item prop="cluster">
                <el-select v-model="dialogCloneForm.sourceCluster" placeholder="选择k8s集群" style="width: 100%;" disabled>
                  <el-option
                    v-for="item in dialogCloneForm.clusterOptions"
                    :key="item.name"
                    :label="item.name + ' (' + item.description + ')'"
                    :value="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="namespace">
                <el-select v-model="dialogCloneForm.sourceNamespace" placeholder="选择运行环境" style="width: 100%" disabled>
                  <el-option
                    v-for="item in dialogCloneForm.namespaceOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="目标环境">
          <el-row>
            <el-col :span="24">
              <el-form-item prop="namespace">
                <el-select v-model="dialogCloneForm.targetNamespaces" multiple filterable placeholder="选择运行环境" style="width: 100%">
                  <el-option
                    v-for="item in dialogCloneForm.namespaceOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
              <div style="color: #888;margin-top: -10px;font-size: 12px">备注：每个环境都会打开一个发布流程新建页面，每个都需要人工进行配置确认和提交</div>
            </el-col>
          </el-row>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogCloneVisible = false">取 消</el-button>
        <el-button type="primary" @click="clonePipeline()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="实例列表" :visible.sync="dialogPodTableVisible" width="60%">
      <pod-simple-table :cluster="dialogPodTable.cluster" :namespace="dialogPodTable.namespace"
                        :app="dialogPodTable.app"></pod-simple-table>
    </el-dialog>
    <el-dialog title="k8s deployment 信息" :visible.sync="dialogK8sDeploymentVisible" width="60%">
      <deployment-detail :cluster="dialogK8sDeployment.cluster" :namespace="dialogK8sDeployment.namespace"
                         :app="dialogK8sDeployment.app"></deployment-detail>
    </el-dialog>

    <el-dialog title="创建Bugfix开发分支" :visible.sync="bugfixBranchVisible" width="60%">
      <div style="margin-bottom: 10px;">
        <el-alert
          title="提示说明"
          type="info"
          show-icon
          description="创建Bugfix分支时，会以当前环境的版本为基础，创建一个新的Git分支。然后大家在此分支上进行代码修复、提交和发布，避免误把其他环境的代码引入到当前环境。"
          effect="dark">
        </el-alert>
      </div>
      <el-form label-width="80px">
        <el-form-item label="应用">
          <el-input v-model="bugfixBranch.app" disabled></el-input>
        </el-form-item>
        <el-form-item label="集群">
          <el-input v-model="bugfixBranch.cluster" disabled></el-input>
        </el-form-item>
        <el-form-item label="环境">
          <el-input v-model="bugfixBranch.namespace" disabled></el-input>
        </el-form-item>
        <el-form-item label="Git地址">
          <el-select v-model="bugfixBranch.gitUrl" placeholder="选择部署模块的Git地址" style="width: 100%;">
            <el-option
              v-for="item in bugfixBranch.gitUrlOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="bugfixBranchVisible = false">取 消</el-button>
        <el-button type="primary" @click="createBugfixBranch()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="确定要下线发布流程吗？"
      :visible.sync="removePipeDialogVisible"
      width="30%">
      <p>应用：<b>{{ this.removePipeDialogData.app }}</b></p>
      <p>环境：<b>{{ this.removePipeDialogData.namespace }} ({{ this.removePipeDialogData.cluster }})</b></p>
      <p style="color: orangered">提示：</p>
      <div style="padding-left: 10px;color: orangered">
        <p><b>1. 只有运行副本数为0，才能正常下线</b></p>
        <p><b>2. 下线后，会删除掉发布流程</b></p>
        <p><b>3. 当前操作需要系统管理员权限，业务同学请按照页面中的【服务下线说明】的进行操作。</b></p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="removePipeDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="removePipe()">继续下线</el-button>
      </span>
    </el-dialog>

    <el-dialog title="服务下线说明" :visible.sync="offlineAppDialogVisible" width="30%">
      <div style="line-height: 2em;">
        如果服务已不再使用，请申请下线，避免资源浪费。下线方式： 通过CRM系统的【k8s应用下线申请】对象进行申请。<br/>
        对象地址：
        <a style="color: #409EFF;text-decoration: underline;" href="https://www.fxiaoke.com/XV/UI/Home#crm/list/=/k8s_application_offline_a__c" target="_blank">
          https://www.fxiaoke.com/XV/UI/Home#crm/list/=/k8s_application_offline_a__c
        </a>
      </div>
    </el-dialog>
    <!--  修改状态的dialog  -->
    <el-dialog
      title="确定要修改发布流程状态吗？"
      :visible.sync="updateStatusDialogVisible"
      width="30%">
      <p>应用：<b>{{ this.updateStatusDialogData.app }}</b></p>
      <p>环境：<b>{{ this.updateStatusDialogData.namespace }} ({{ this.updateStatusDialogData.cluster }})</b></p>
      <el-select v-model="updateStatusDialogData.status" placeholder="请选择">
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updateStatusDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="updateStatus()">确认</el-button>
      </span>
    </el-dialog>

    <el-dialog title="构建+发布" :visible.sync="buildAndDeployDialogVisible" width="960px" top="2vh">
      <div style="margin: -40px -20px -20px;">
        <build-and-deploy :pipeline-id="buildAndDeployPipelineId" @successHandler="buildAndDeployDialogVisible=false"></build-and-deploy>
      </div>
    </el-dialog>


    <el-drawer
      :withHeader="false"
      :destroy-on-close="true"
      :visible.sync="addressVisible"
      direction="btt"
      size="400px">
      <app-address :cluster="address.cluster" :namespace="address.namespace" :app="address.app" icon="el-icon-link"></app-address>
    </el-drawer>
  </div>
</template>

<script>

import {findPipelinesByApp, removePipeline, updatePipelineStatus} from "@/api/pipeline";
import {cloneObject} from "@/utils/my-util";
import PodSimpleTable from '@/views/components/pod-simple-table'
import {deploymentDetail} from "@/api/k8s/app";
import PipelineExpand from "@/views/pipeline/pipeline-expand";
import PipelineDoc from "@/views/pipeline/pipeline-doc";
import PipelineApp from "@/views/pipeline/pipeline-app";
import {deployApp, getImageOptions, searchJob} from "@/api/job";
import AppSelector2 from "@/views/cicd/app-selector2.vue";
import JobRunnerAlert from "@/views/cicd/job-running-alert.vue";
import MenuTabs from "@/views/cicd/menu-tabs.vue";
import AppAddress from "@/views/components/app-address.vue";
import DeploymentDetail from "@/views/app/deployment-detail.vue";
import {createBugfixBranch} from "@/api/app";
import {searchAppAllScale} from "@/api/k8s/scale";
import ExportButton from "@/views/components/export-button.vue";
import BuildAndDeploy from "@/views/cicd/build-and-deploy.vue";
import MaintainAlert from "@/views/cicd/manitain-alert.vue";
import {getDeregisterPodByApp} from "@/api/k8s/pod";

export default {
  name: "app-deploy",
  data() {
    return {
      loading: false,
      statusOptions: [
        {label: "所有", value: "_all_"},
        {label: "可用", value: "enabled"},
        {label: "禁用", value: "disabled"},
        {label: "已迁移", value: "migrated"},
      ],
      statusFilter: "_all_",
      cloudCategories: [],
      cloudCategoryCurr: "_all_",
      currApp: "",
      tableDataAll: [],
      tableData: [],
      tableLoading: false,
      tableColumnsShowMore: {
        baseImage: false,
        createTime: false,
        jvmOpts: false,
      },
      deployData: {
        dialogVisible: false,
        dialogLoading: false,
        form: {
          pipelineIds: [],
          maxSurge: "50%",
          remark: "",
          eolinkerTest: false,
          deployModuleImages: [
            {
              gitUrl: "",
              gitModule: "",
              image: ""
            }
          ]
        },
        pipelines: [],
        imageOptions: [
          {
            gitUrl: "",
            gitModule: "",
            imageSelected: "",
            images: [
              {
                name: "",
                tag: "",
                parentPom: "",
                parentPomShortName: "",
                remark: ""
              }
            ]
          }
        ]
      },
      removePipeDialogVisible: false,
      removePipeDialogData: {},
      offlineAppDialogVisible: false,
      addressVisible: false,
      address: {
        cluster: "",
        namespace: "",
        app: "",
      },
      dialogCloneVisible: false,
      dialogCloneForm: {
        clusterOptions: [],
        namespaceOptions: [],
        app: "",
        sourcePipelineId: 0,
        sourceCluster: "",
        sourceNamespace: "",
        targetNamespaces: [],
      },
      dialogPodTableVisible: false,
      dialogPodTable: {
        cluster: "",
        namespace: "",
        app: "",
      },
      dialogK8sDeploymentVisible: false,
      dialogK8sDeployment: {
        cluster: "",
        namespace: "",
        app: "",
      },
      bugfixBranchVisible: false,
      bugfixBranch: {
        gitUrl: "",
        namespace: "",
        cluster: "",
        app: "",
        gitUrlOptions: []
      },
      // 修改状态相关数据
      updateStatusDialogVisible: false,
      updateStatusDialogData: {},
      buildAndDeployDialogVisible: false,
      buildAndDeployPipelineId: 0,
    }
  },
  components: {
    MaintainAlert,
    BuildAndDeploy,
    ExportButton,
    DeploymentDetail,
    AppAddress,
    MenuTabs,
    JobRunnerAlert,
    AppSelector2,
    PipelineApp,
    PipelineDoc,
    PodSimpleTable,
    PipelineExpand
  },
  computed: {
    isProdEnv() {
      return window.location.host.indexOf("foneshare") > -1;
    },
    cicdOneKey() {
      return !this.isProdEnv
    }
  },
  mounted() {
    // let app = this.$route.query.app
    // if (app) {
    //   this.currApp = app
    // }
  },
  methods: {
    changeCurrApp(app) {
      this.currApp = app
      this.updateQueryParam()
      this.loadTableData()
    },
    pipelineFilter() {
      let data = this.tableDataAll
      let dataFilter = []
      if (this.cloudCategoryCurr === "_all_") {
        dataFilter = data
      } else {
        for (let it of data) {
          if (it.extraAttr.cloudCategory === this.cloudCategoryCurr) {
            dataFilter.push(it)
          }
        }
      }
      data = dataFilter
      dataFilter = []
      if (this.statusFilter === "_all_") {
        dataFilter = data
      } else {
        for (let it of this.tableDataAll) {
          if (it.status === this.statusFilter) {
            dataFilter.push(it)
          }
        }
      }
      this.tableData = dataFilter
    },
    updateQueryParam() {
      let urlParam = cloneObject(this.$route.query)
      urlParam["app"] = this.currApp;
      this.$router.push({query: urlParam});
    },
    cloudCategoryStatistics(pipes) {
      let data = []
      for (let pi of pipes) {
        let items = data.filter(it => it.name === pi.extraAttr.cloudCategory)
        if (items.length < 1) {
          data.push({
            "name": pi.extraAttr.cloudCategory,
            "desc": pi.extraAttr.cloudCategoryDesc,
            "pipelineCount": 1
          })
          continue
        }
        items[0].pipelineCount += 1
      }
      this.cloudCategories = data
    },
    loadTableData() {
      this.tableLoading = true
      findPipelinesByApp(this.currApp).then(response => {
        //为了展开行时，能够动态渲染懒加载后的pod数据， 这里需要提前设置好pipeline的pods属性值
        //ref1: https://blog.csdn.net/weixin_47711284/article/details/106376501
        //ref2: https://github.com/ElemeFE/element/issues/3925
        response.data.map(it => {
          it.pods = [];
          it.extraAttr.deployTag = null
          it.extraAttr.deployModules = []
          it.extraAttr.runningPodNum = null
          it.extraAttr.autoScaleV2MinReplicas = null
          it.extraAttr.autoScaleV2MaxReplicas = null
          it.extraAttr.cronScaleMinReplicas = null
          it.extraAttr.cronScaleMaxReplicas = null
          it.extraAttr.deregisterPodSize = 0
          it.extraAttr.deployStatus = null
          it.extraAttr.deployJobId = null
        });

        this.cloudCategoryStatistics(response.data)
        this.tableDataAll = response.data;
        this.tableData = this.tableDataAll;
        for (let it of this.tableData) {
          this.findDeployment(it.cluster, it.namespace, it.app)
          this.findScaleConfig(it.cluster, it.namespace, it.app)
          this.findDeregisterPods(it.cluster, it.namespace, it.app)
        }
        this.loadCDJobs()
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    loadCDJobs() {
      if (!this.currApp) {
        return
      }
      let params = {
        "params": {},
        "app": this.currApp,
        "status": ["WAIT", "RUNNING"],
        "type": "CD",
        "page": 1,
        "limit": 100,
      }
      searchJob(params).then(response => {
        let jobs = response.data.data
        for (let j of jobs) {
          for (let p of this.tableData) {
            if (p.id === j.pipelineId) {
              let deployStatus = null;
              if (j.status === "RUNNING") {
                deployStatus = "发布中•••"
              } else if (j.status === "WAIT") {
                deployStatus = "发布等待中•••"
              }
              p.extraAttr.deployStatus = deployStatus
              p.extraAttr.deployJobId = j.id
              break
            }
          }
        }
      }).catch((e) => {
        console.log("load job fail, " + e.message)
      })
    },
    urgentDeploy() {
      this.$alert('如果需要紧急发布，请申请临时操作权限。 点击 <a href="/#/auth/temp-path" target="_blank" style="color: #3a8ee6">链接</a> 打开临时授权申请页面', '紧急发布提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '关闭'
      });
    },
    findDeployment(cluster, namespace, app) {
      let pipe = null
      for (let it of this.tableData) {
        if (it.cluster === cluster && it.namespace === namespace && it.app === app) {
          pipe = it
          break
        }
      }
      if (pipe === null) {
        return
      }
      deploymentDetail(cluster, namespace, app).then(response => {
        pipe.extraAttr.deployTag = response.data.deployTag
        pipe.extraAttr.deployModules = response.data.deployModules
        pipe.extraAttr.runningPodNum = response.data.replicas
        pipe.extraAttr.lastDeployTime = response.data.deployTime
        pipe.extraAttr.lastDeployUser = response.data.deployUser
      }).catch((e) => {
        pipe.extraAttr.deployTag = "--"
        pipe.extraAttr.deployModules = []
        pipe.extraAttr.runningPodNum = 0
        pipe.extraAttr.lastDeployTime = null;
        pipe.extraAttr.lastDeployUser = null;
        console.error(e.message)
      });
    },
    findDeregisterPods(cluster, namespace, app) {
      let pipe = null
      for (let it of this.tableData) {
        if (it.cluster === cluster && it.namespace === namespace && it.app === app) {
          pipe = it
          break
        }
      }
      if (pipe === null) {
        return
      }
      getDeregisterPodByApp(cluster, namespace, app, false).then(response => {
        pipe.extraAttr.deregisterPodSize = response.data.length;
      }).catch((e) => {
      });
    },
    findScaleConfig(cluster, namespace, app) {
      let pipe = null
      for (let it of this.tableData) {
        if (it.cluster === cluster && it.namespace === namespace && it.app === app) {
          pipe = it
          break
        }
      }
      if (pipe === null) {
        return
      }
      searchAppAllScale(cluster, namespace, app).then(response => {
        if (response.data['autoScaleV2']) {
          pipe.extraAttr.autoScaleV2MinReplicas = response.data['autoScaleV2']['spec']['minReplicaCount']
          pipe.extraAttr.autoScaleV2MaxReplicas = response.data['autoScaleV2']['spec']['maxReplicaCount']
        } else {
          pipe.extraAttr.autoScaleV2MinReplicas = "-"
          pipe.extraAttr.autoScaleV2MaxReplicas = "-"
        }
        if (response.data['cronScale']) {
          pipe.extraAttr.cronScaleMinReplicas = pipe.replicas
          pipe.extraAttr.cronScaleMaxReplicas = response.data['cronScale']['Replicas']
        } else {
          pipe.extraAttr.cronScaleMinReplicas = "-"
          pipe.extraAttr.cronScaleMaxReplicas = "-"
        }
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    removePipe() {
      this.$prompt('请输入应用名', '下线确认提示', {
        confirmButtonText: '继续下线',
        confirmButtonClass: 'el-button--danger',
        cancelButtonText: '取消',
      }).then(({value}) => {
        if (value !== this.removePipeDialogData.app) {
          this.$message.info("应用名输入错误")
          return
        }
        removePipeline(this.removePipeDialogData.id).then(response => {
          this.$message.success("删除成功")
          this.loadTableData(this.$route.query.app)
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.removePipeDialogVisible = false
        })
      }).catch(() => {
        console.log("取消下线确认操作")
      });
    },
    removePipeDialog(row) {
      this.removePipeDialogData = cloneObject(row)
      this.removePipeDialogVisible = true
    },
    offlineAppDialog() {
      this.offlineAppDialogVisible = true
    },
    updateStatusDialog(row) {
      this.updateStatusDialogData = cloneObject(row)
      this.updateStatusDialogVisible = true
    },
    updateStatus() {
      let req = {}
      req.id = this.updateStatusDialogData.id
      req.status = this.updateStatusDialogData.status
      console.log(req)
      updatePipelineStatus(req).then(response => {
        this.$message.success("修改成功")
        this.loadTableData(this.$route.query.app)
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.updateStatusDialogVisible = false
      })
    },
    createPage() {
      this.$router.push({name: 'app-pipeline-edit', query: {"app": this.currApp}});
    },
    tempAuthPage() {
      let href = this.$router.resolve({
        name: 'auth-temp-auth',
        query: {"showAddDialog": "true", "app": this.currApp}
      }).href
      window.open(href, '_blank');
    },
    batchDeploy() {
      let rows = this.$refs.pipelineTable.store.states.selection;
      if (!rows || rows.length < 1) {
        this.$message.warning("请选择需要发布的流程");
        return
      }
      this.showDeployDialog(rows)
    },
    imageBuildPage() {
      let url = this.$router.resolve({name: 'cicd-image-build', query: {'app': this.currApp, 'openDialog': 'true'}}).href
      window.open(url, '_blank')
    },
    multiClusterPodExecPage() {
      let rows = this.$refs.pipelineTable.store.states.selection;
      if (!rows || rows.length < 1) {
        this.$message.warning("请选择需要发布的流程");
        return
      }
      this.$confirm(`此操作将同时进入 ${rows.length} 个发布流程的第一个实例, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let paramItems = []
        for (let row of rows) {
          paramItems.push({
            "cluster": row.cluster,
            "namespace": row.namespace,
            "app": row.app,
          })
        }
        let params = JSON.stringify(paramItems)
        let url = `/api/page/redirect?type=webShellWithFirstPod&webShellParams=${params}&_t` + Date.now();
        window.open(url)
      }).catch(() => {
      });
    },
    editPage(row) {
      let p = {
        "pipelineId": row.id,
      };
      this.$router.push({name: 'app-pipeline-edit', query: p});
    },
    syncConfigToOtherPage(row) {
      let p = {
        "pipelineId": row.id,
        "app": row.app
      };
      this.$router.push({name: 'app-pipeline-sync-config', query: p});
    },
    showPodTable(row) {
      this.dialogPodTable.cluster = row.cluster;
      this.dialogPodTable.namespace = row.namespace;
      this.dialogPodTable.app = row.app;
      this.dialogPodTableVisible = true;
    },
    scalePage(row, category) {
      let rou = null;
      if (category === "autoscalev2") {
        rou = this.$router.resolve({name: 'pod-auto-scaler', query: {"cluster": row.cluster, namespace: row.namespace, app: row.app}});
      } else if (category === "cronscale") {
        rou = this.$router.resolve({name: 'app-scale-cron', query: {"cluster": row.cluster, namespace: row.namespace, app: row.app}});
      } else {
        this.$message.error("未知的扩缩容类型：" + category)
        return
      }
      window.open(rou.href, '_blank');
    },
    showK8sDeployment(row) {
      this.dialogK8sDeployment.cluster = row.cluster;
      this.dialogK8sDeployment.namespace = row.namespace;
      this.dialogK8sDeployment.app = row.app;
      this.dialogK8sDeploymentVisible = true;
    },
    showCreateBugfixBranch(row) {
      this.bugfixBranch.cluster = row.cluster;
      this.bugfixBranch.namespace = row.namespace;
      this.bugfixBranch.app = row.app;
      this.bugfixBranchVisible = true;
      this.bugfixBranch.gitUrlOptions = []
      for (let it of row.appModules) {
        if (!this.bugfixBranch.gitUrlOptions.includes(it.gitUrl)) {
          this.bugfixBranch.gitUrlOptions.push(it.gitUrl)
        }
      }
    },
    createBugfixBranch() {
      createBugfixBranch(this.bugfixBranch).then(response => {
        this.$message.success("创建成功，分支名：" + response.data + "。3秒后跳转到gitlab页面,请确保浏览器允许弹出新窗口")
        setTimeout(() => {
          window.open(this.bugfixBranch.gitUrl.replace(".git", "") + "/-/branches", '_blank');
        }, 3000)
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.bugfixBranchVisible = false
      })
    },
    showDeployDialog(rows) {
      let firstRow = rows[0]
      for (let it of rows) {
        if (it.status !== "enabled") {
          this.$message.warning("有发布流程处于不可用状态")
          return
        }
        if (it.appModules.length !== firstRow.appModules.length) {
          this.$message.warning("发布流程之间的部署模块不一样，不能一起批量发布")
          return
        }
      }
      this.loading = true;
      let pipeIds = rows.map(x => x.id);
      getImageOptions(pipeIds.join(",")).then(response => {
        this.loading = false
        this.deployData.pipelines = rows
        this.deployData.imageOptions = response.data
        let maxSurge = "50%"
        if (this.$settings.maxSurgeForceFull) {
          maxSurge = "100%"
        }
        let eolinkerTest = false
        if (this.$settings.eolinkerTestDefault) {
          eolinkerTest = true
        }
        this.deployData.form = {
          pipelineIds: pipeIds,
          maxSurge: maxSurge,
          remark: "",
          eolinkerTest: eolinkerTest,
          deployModuleImages: []
        }
        this.deployData.dialogVisible = true
      }).catch((e) => {
        this.loading = false
        this.$message.error(e.message);
      });
    },
    imageTagChange(v) {
      for (let it of this.deployData.imageOptions) {
        for (let image of it.images) {
          if (image.name === v && image.remark) {
            this.deployData.form.remark = image.remark
            break
          }
        }
      }
    },
    manageDropdownCommand(command) {
      console.log("manage dropdown command: " + command);
      let parts = command.split("##");
      if (parts.length !== 2) {
        return
      }
      let cmd = parts[0];
      let id = parseInt(parts[1]);
      let row = this.tableData.filter(item => {
        return item.id === id
      })[0]
      if (cmd === "edit") {
        this.editPage(row)
      } else if (cmd === "showAddr") {
        this.showAddress(row)
      } else if (cmd === "remove") {
        this.removePipeDialog(row)
      } else if (cmd === "clone") {
        this.showCloneDialog(row)
      } else if (cmd === "deployHistory") {
        this.jobHistoryPage(row.app, row.namespace)
      } else if (cmd === "address") {
        this.showAddress(row)
      } else if (cmd === "updateStatus") {
        this.updateStatusDialog(row)
      } else if (cmd === "syncConfigToOther") {
        this.syncConfigToOtherPage(row)
      } else if (cmd === "appLog") {
        this.clickhouseLogPage("app_log", row.cluster, row.namespace, row.app)
      } else if (cmd === "k8sDeployment") {
        this.showK8sDeployment(row)
      } else if (cmd === "bugfixBranch") {
        this.showCreateBugfixBranch(row)
      } else {
        this.$message.error('未知操作：' + command);
      }
    },
    deploySubmit() {
      this.$refs['dialogDeployForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        for (let it of this.deployData.imageOptions) {
          if (!it.imageSelected) {
            this.$message.error("请选择镜像版本")
            return false
          }
        }

        let envConfirmText = this.$settings.envConfirmText
        if (envConfirmText) {
          this.$prompt(`请把红色内容输入到文本框: <b style="color:orangered">${envConfirmText}</b> `, '环境确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true
          }).then(({value}) => {
            if (value !== envConfirmText) {
              this.$message.warning("环境确认失败，请重新输入")
              return
            }
            this.processDeploy();
          })
        } else {
          this.processDeploy();
        }
      });
    },
    processDeploy() {
      this.deployData.dialogLoading = true;
      let deployModuleImages = [];
      for (let it of this.deployData.imageOptions) {
        deployModuleImages.push({
          gitUrl: it.gitUrl,
          gitModule: it.gitModule,
          image: it.imageSelected
        })
      }

      let params = {items: []}
      for (let it of this.deployData.form.pipelineIds) {
        params.items.push({
          pipelineId: it,
          maxSurge: this.deployData.form.maxSurge,
          remark: this.deployData.form.remark,
          eolinkerTest: this.deployData.form.eolinkerTest,
          deployModuleImages: deployModuleImages
        })
      }
      deployApp(params).then(response => {
        if (response.data.length === 1) {
          this.jobPage(response.data[0].jobId)
        } else {
          this.jobHistoryPage(this.currApp, "")
        }
        this.deployData.dialogVisible = false
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.$refs['jobRunnerAlert'].loadJobs();
        this.loadCDJobs();
        this.deployData.dialogLoading = false;
      })
    },
    jobPage(id) {
      let rou = this.$router.resolve({name: 'cicd-app-deploy-detail', query: {"jobId": id}});
      window.open(rou.href, '_blank');
    },
    jobHistoryPage(app, namespace) {
      let rou = this.$router.resolve({name: 'cicd-app-deploy-history', query: {"app": app, "namespace": namespace}});
      window.open(rou.href, '_blank');
    },
    showCloneDialog(row) {
      this.dialogCloneForm.app = row.app
      this.dialogCloneForm.sourcePipelineId = row.id
      this.dialogCloneForm.sourceCluster = row.cluster
      this.dialogCloneForm.sourceNamespace = row.namespace

      let nsOptions = []
      for (let clu of this.$settings.clusters) {
        for (let ns of clu.namespaces) {
          //如果对应环境的发布流程已存在，则不放入选择集合里
          if (this.tableData.filter(item => {
            return item.cluster === clu.name && item.namespace === ns
          }).length > 0) {
            console.log(`pipeline has exist, remove namespace option: ${clu.name}/${ns}`)
            continue
          }
          nsOptions.push(`${clu.name}/${ns}`)
        }
      }
      this.dialogCloneForm.namespaceOptions = nsOptions
      this.dialogCloneVisible = true
    },
    buildAndDeployDialog(row) {
      this.buildAndDeployPipelineId = row.id
      this.buildAndDeployDialogVisible = true
    },
    clonePipeline() {
      let f = this.dialogCloneForm
      if (!f.sourcePipelineId) {
        this.$message.error("源环境信息缺失")
        return
      }
      if (!f.targetNamespaces) {
        this.$message.error("请选择目标环境 ")
        return
      }

      for (let it of f.targetNamespaces) {
        let parts = it.split("/")
        if (parts.length !== 2) {
          this.$message.error("目标环境格式错误")
          return
        }
        let routeUrl = this.$router.resolve({
          name: "app-pipeline-edit",
          query: {
            "operate": "clone",
            "pipelineId": f.sourcePipelineId,
            "targetCluster": parts[0],
            "targetNamespace": parts[1]
          }
        });
        window.open(routeUrl.href, '_blank');
        setTimeout(() => {
          this.dialogCloneVisible = false
        }, 500)
      }
    },
    clickhouseLogPage(logName, cluster, namespace, app) {
      let url = `/api/page/redirect?type=clickhouse&logName=${logName}&cluster=${cluster}&namespace=${namespace}&app=${app}&_t` + Date.now();
      window.open(url)
    },
    showAddress(row) {
      this.address = {
        cluster: row.cluster,
        namespace: row.namespace,
        app: row.app,
      }
      this.addressVisible = true;
    },
    convertStatus(status) {
      switch (status) {
        case "enabled":
          return "可用"
        case "disabled":
          return "禁用"
        case "audit":
          return "待审核"
        case "migrated":
          return "已迁移"
        default:
          return "未知"
      }
    },
    cellStyle({row, column, rowIndex, columnIndex}) {
      if (column.label === "运行数" && row.extraAttr && row.extraAttr.runningPodNum < 1) {
        return "background-color:#ffb980";
      }
    }
  }
}
</script>

<style>

/*expand row*/
/*.app-deploy-container .expand-wrapper .el-form-item--mini.el-form-item,*/
/*.app-deploy-container.expand-wrapper .el-form-item--small.el-form-item {*/
/*  margin-bottom: 5px;*/
/*}*/

.app-deploy-container .app_info_panel {
  color: #909399;
  margin: 10px 0;
  font-size: 14px;
}

.app-deploy-container .app_info_panel b {
  margin: 0 10px;
  padding: 5px 10px;
  background-color: #eee;
}

.app-deploy-container .divider-blockquote > span {
  color: #409EFF;
  margin-left: 5px;
  font-weight: bold;
}


.app-deploy-container .deploy-dialog .el-form-item {
  margin-bottom: 16px;
}

.app-deploy-container .deploy-dialog .el-form-item.module-git-url .el-form-item__content {
  line-height: 24px;
}

.app-deploy-container .deploy-dialog .el-form-item.module-git-url {
  margin-bottom: 0;
}

.app-deploy-container .deploy-dialog .el-form-item.module-git-url .el-form-item__content {
  line-height: 26px;
}

/*dropdown button*/
.app-deploy-container .el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.app-deploy-container .el-icon-arrow-down {
  font-size: 12px;
}

.app-deploy-container .el-table td, .el-table th {
  padding: 5px 0;
}

.app-deploy-container .dedicated-cloud-row {
  background-color: oldlace;
}

.app-deploy-container .el-table .el-table__cell {
  padding: 0;
}

.el-drawer {
  overflow: scroll
}

.app-deploy-container .question-icon-size {
  font-size: 12px;
}

.env-divider.el-divider--horizontal {
  margin: 10px 0;
}

.app-deploy-container .el-form-item__error {
  background-color: white;
  z-index: 9999;
}

.el-popper[x-placement^=bottom] {
  margin-top: 0;
}


.app-deploy-container .el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
  background-color: #e2f0ff;
}

.deploy-dialog .el-dialog__body {
  padding: 10px;
}

.fxiaokeCloud.el-tag {
  color: #bbb;
  border-color: #bbb;
}

.publicCloud.el-tag {
  color: #e36a08;
  border-color: #e36a08;
}

.dedicatedCloud.el-tag {
  color: #09a12e;
  border-color: #09a12e;
}

.hybirdCloud.el-tag {
  color: #a924de;
  border-color: #a924de;
}

.app-deploy-container .env-label-alert {
  background-color: #fffbe6;
  border: solid 1px #ffe58f;
  padding: 0 3px;
  margin: 5px 0;
  color: rgba(0, 0, 0, 0.88);
  line-height: 10px;
}

.app-deploy-container .env-label-alert .el-alert__icon {
  color: #666;
}

.app-deploy-container .env-label-alert .el-alert__title {
  font-size: 10px;
}
</style>
